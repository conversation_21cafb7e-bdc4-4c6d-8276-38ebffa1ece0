<testsuites id="" name="" tests="28" failures="28" skipped="0" errors="0" time="71.62926399999999">
<testsuite name="comprehensive-priority-tests.spec.ts" timestamp="2025-09-06T04:56:13.710Z" hostname="chromium" tests="4" failures="4" skipped="0" time="19.727" errors="0">
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" classname="comprehensive-priority-tests.spec.ts" time="4.904">
<failure message="comprehensive-priority-tests.spec.ts:136:7 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-priority-tests.spec.ts:136:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:163:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" classname="comprehensive-priority-tests.spec.ts" time="4.829">
<failure message="comprehensive-priority-tests.spec.ts:238:7 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-priority-tests.spec.ts:238:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation) 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:251:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\test-failed-2.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications" classname="comprehensive-priority-tests.spec.ts" time="5.284">
<failure message="comprehensive-priority-tests.spec.ts:300:7 🎯 PRIORITY 3: Group Chat Creation and Notifications" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-priority-tests.spec.ts:300:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:315:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\test-failed-4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 3: Group Chat Creation and Notifications

📝 Step 1: Login all three users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\test-failed-4.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test" classname="comprehensive-priority-tests.spec.ts" time="4.71">
<failure message="comprehensive-priority-tests.spec.ts:405:7 🎯 COMPREHENSIVE: All Features Integration Test" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-priority-tests.spec.ts:405:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:417:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting COMPREHENSIVE: All Features Integration Test

📝 Step 1: Complete authentication flow
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\test-failed-3.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-priority-tests.spec.ts" timestamp="2025-09-06T04:56:13.710Z" hostname="firefox" tests="4" failures="4" skipped="0" time="118.757" errors="0">
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" classname="comprehensive-priority-tests.spec.ts" time="30.061">
<failure message="comprehensive-priority-tests.spec.ts:136:7 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" type="FAILURE">
<![CDATA[  [firefox] › comprehensive-priority-tests.spec.ts:136:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      108 | test.describe('🚀 Comprehensive Priority E2E Tests - Chat Application', () => {
      109 |   
    > 110 |   test.beforeEach(async ({ page }) => {
          |        ^
      111 |     // Set up network monitoring
      112 |     await page.route('**/*', (route) => {
      113 |       const url = route.request().url();
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:110:8

    Error: browserContext.newPage: Test timeout of 30000ms exceeded.
]]>
</failure>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" classname="comprehensive-priority-tests.spec.ts" time="28.383">
<failure message="comprehensive-priority-tests.spec.ts:238:7 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" type="FAILURE">
<![CDATA[  [firefox] › comprehensive-priority-tests.spec.ts:238:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation) 

    Error: page.goto: NS_ERROR_NET_EMPTY_RESPONSE
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:251:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\test-failed-2.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications" classname="comprehensive-priority-tests.spec.ts" time="30.06">
<failure message="comprehensive-priority-tests.spec.ts:300:7 🎯 PRIORITY 3: Group Chat Creation and Notifications" type="FAILURE">
<![CDATA[  [firefox] › comprehensive-priority-tests.spec.ts:300:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      108 | test.describe('🚀 Comprehensive Priority E2E Tests - Chat Application', () => {
      109 |   
    > 110 |   test.beforeEach(async ({ page }) => {
          |        ^
      111 |     // Set up network monitoring
      112 |     await page.route('**/*', (route) => {
      113 |       const url = route.request().url();
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:110:8

    Error: browserContext.newPage: Test timeout of 30000ms exceeded.
]]>
</failure>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test" classname="comprehensive-priority-tests.spec.ts" time="30.253">
<failure message="comprehensive-priority-tests.spec.ts:405:7 🎯 COMPREHENSIVE: All Features Integration Test" type="FAILURE">
<![CDATA[  [firefox] › comprehensive-priority-tests.spec.ts:405:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      108 | test.describe('🚀 Comprehensive Priority E2E Tests - Chat Application', () => {
      109 |   
    > 110 |   test.beforeEach(async ({ page }) => {
          |        ^
      111 |     // Set up network monitoring
      112 |     await page.route('**/*', (route) => {
      113 |       const url = route.request().url();
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:110:8

    Error: browserContext.newPage: Test timeout of 30000ms exceeded.
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="comprehensive-priority-tests.spec.ts" timestamp="2025-09-06T04:56:13.710Z" hostname="webkit" tests="4" failures="4" skipped="0" time="54.612" errors="0">
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" classname="comprehensive-priority-tests.spec.ts" time="14.318">
<failure message="comprehensive-priority-tests.spec.ts:136:7 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" type="FAILURE">
<![CDATA[  [webkit] › comprehensive-priority-tests.spec.ts:136:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('form') to be visible


      32 |   
      33 |   // Wait for login form to be visible
    > 34 |   await page.waitForSelector('form', { timeout: 10000 });
         |              ^
      35 |   
      36 |   // Fill login form
      37 |   await page.fill('[data-testid="email-input"]', email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:34:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:163:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" classname="comprehensive-priority-tests.spec.ts" time="13.919">
<failure message="comprehensive-priority-tests.spec.ts:238:7 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" type="FAILURE">
<![CDATA[  [webkit] › comprehensive-priority-tests.spec.ts:238:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation) 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('form') to be visible


      32 |   
      33 |   // Wait for login form to be visible
    > 34 |   await page.waitForSelector('form', { timeout: 10000 });
         |              ^
      35 |   
      36 |   // Fill login form
      37 |   await page.fill('[data-testid="email-input"]', email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:34:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:251:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications" classname="comprehensive-priority-tests.spec.ts" time="13.554">
<failure message="comprehensive-priority-tests.spec.ts:300:7 🎯 PRIORITY 3: Group Chat Creation and Notifications" type="FAILURE">
<![CDATA[  [webkit] › comprehensive-priority-tests.spec.ts:300:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('form') to be visible


      32 |   
      33 |   // Wait for login form to be visible
    > 34 |   await page.waitForSelector('form', { timeout: 10000 });
         |              ^
      35 |   
      36 |   // Fill login form
      37 |   await page.fill('[data-testid="email-input"]', email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:34:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:315:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\test-failed-4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 3: Group Chat Creation and Notifications

📝 Step 1: Login all three users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\test-failed-4.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test" classname="comprehensive-priority-tests.spec.ts" time="12.821">
<failure message="comprehensive-priority-tests.spec.ts:405:7 🎯 COMPREHENSIVE: All Features Integration Test" type="FAILURE">
<![CDATA[  [webkit] › comprehensive-priority-tests.spec.ts:405:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('form') to be visible


      32 |   
      33 |   // Wait for login form to be visible
    > 34 |   await page.waitForSelector('form', { timeout: 10000 });
         |              ^
      35 |   
      36 |   // Fill login form
      37 |   await page.fill('[data-testid="email-input"]', email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:34:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:417:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting COMPREHENSIVE: All Features Integration Test

📝 Step 1: Complete authentication flow
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\test-failed-2.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-priority-tests.spec.ts" timestamp="2025-09-06T04:56:13.710Z" hostname="Mobile Chrome" tests="4" failures="4" skipped="0" time="10.229" errors="0">
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" classname="comprehensive-priority-tests.spec.ts" time="2.653">
<failure message="comprehensive-priority-tests.spec.ts:136:7 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" type="FAILURE">
<![CDATA[  [Mobile Chrome] › comprehensive-priority-tests.spec.ts:136:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:163:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" classname="comprehensive-priority-tests.spec.ts" time="2.269">
<failure message="comprehensive-priority-tests.spec.ts:238:7 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" type="FAILURE">
<![CDATA[  [Mobile Chrome] › comprehensive-priority-tests.spec.ts:238:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation) 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:251:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications" classname="comprehensive-priority-tests.spec.ts" time="3.09">
<failure message="comprehensive-priority-tests.spec.ts:300:7 🎯 PRIORITY 3: Group Chat Creation and Notifications" type="FAILURE">
<![CDATA[  [Mobile Chrome] › comprehensive-priority-tests.spec.ts:300:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:315:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\test-failed-4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 3: Group Chat Creation and Notifications

📝 Step 1: Login all three users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\test-failed-4.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test" classname="comprehensive-priority-tests.spec.ts" time="2.217">
<failure message="comprehensive-priority-tests.spec.ts:405:7 🎯 COMPREHENSIVE: All Features Integration Test" type="FAILURE">
<![CDATA[  [Mobile Chrome] › comprehensive-priority-tests.spec.ts:405:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:417:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting COMPREHENSIVE: All Features Integration Test

📝 Step 1: Complete authentication flow
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\test-failed-3.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-priority-tests.spec.ts" timestamp="2025-09-06T04:56:13.710Z" hostname="Mobile Safari" tests="4" failures="4" skipped="0" time="52.298" errors="0">
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" classname="comprehensive-priority-tests.spec.ts" time="13.063">
<failure message="comprehensive-priority-tests.spec.ts:136:7 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" type="FAILURE">
<![CDATA[  [Mobile Safari] › comprehensive-priority-tests.spec.ts:136:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('form') to be visible


      32 |   
      33 |   // Wait for login form to be visible
    > 34 |   await page.waitForSelector('form', { timeout: 10000 });
         |              ^
      35 |   
      36 |   // Fill login form
      37 |   await page.fill('[data-testid="email-input"]', email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:34:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:163:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\test-failed-2.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" classname="comprehensive-priority-tests.spec.ts" time="13.313">
<failure message="comprehensive-priority-tests.spec.ts:238:7 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" type="FAILURE">
<![CDATA[  [Mobile Safari] › comprehensive-priority-tests.spec.ts:238:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation) 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('form') to be visible


      32 |   
      33 |   // Wait for login form to be visible
    > 34 |   await page.waitForSelector('form', { timeout: 10000 });
         |              ^
      35 |   
      36 |   // Fill login form
      37 |   await page.fill('[data-testid="email-input"]', email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:34:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:251:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications" classname="comprehensive-priority-tests.spec.ts" time="12.815">
<failure message="comprehensive-priority-tests.spec.ts:300:7 🎯 PRIORITY 3: Group Chat Creation and Notifications" type="FAILURE">
<![CDATA[  [Mobile Safari] › comprehensive-priority-tests.spec.ts:300:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('form') to be visible


      32 |   
      33 |   // Wait for login form to be visible
    > 34 |   await page.waitForSelector('form', { timeout: 10000 });
         |              ^
      35 |   
      36 |   // Fill login form
      37 |   await page.fill('[data-testid="email-input"]', email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:34:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:315:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\test-failed-4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 3: Group Chat Creation and Notifications

📝 Step 1: Login all three users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\test-failed-4.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test" classname="comprehensive-priority-tests.spec.ts" time="13.107">
<failure message="comprehensive-priority-tests.spec.ts:405:7 🎯 COMPREHENSIVE: All Features Integration Test" type="FAILURE">
<![CDATA[  [Mobile Safari] › comprehensive-priority-tests.spec.ts:405:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('form') to be visible


      32 |   
      33 |   // Wait for login form to be visible
    > 34 |   await page.waitForSelector('form', { timeout: 10000 });
         |              ^
      35 |   
      36 |   // Fill login form
      37 |   await page.fill('[data-testid="email-input"]', email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:34:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:417:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting COMPREHENSIVE: All Features Integration Test

📝 Step 1: Complete authentication flow
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\test-failed-3.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-priority-tests.spec.ts" timestamp="2025-09-06T04:56:13.710Z" hostname="Microsoft Edge" tests="4" failures="4" skipped="0" time="11.969" errors="0">
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" classname="comprehensive-priority-tests.spec.ts" time="2.956">
<failure message="comprehensive-priority-tests.spec.ts:136:7 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" type="FAILURE">
<![CDATA[  [Microsoft Edge] › comprehensive-priority-tests.spec.ts:136:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:163:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" classname="comprehensive-priority-tests.spec.ts" time="2.892">
<failure message="comprehensive-priority-tests.spec.ts:238:7 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" type="FAILURE">
<![CDATA[  [Microsoft Edge] › comprehensive-priority-tests.spec.ts:238:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation) 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:251:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications" classname="comprehensive-priority-tests.spec.ts" time="3.377">
<failure message="comprehensive-priority-tests.spec.ts:300:7 🎯 PRIORITY 3: Group Chat Creation and Notifications" type="FAILURE">
<![CDATA[  [Microsoft Edge] › comprehensive-priority-tests.spec.ts:300:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:315:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\test-failed-4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 3: Group Chat Creation and Notifications

📝 Step 1: Login all three users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\test-failed-4.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test" classname="comprehensive-priority-tests.spec.ts" time="2.744">
<failure message="comprehensive-priority-tests.spec.ts:405:7 🎯 COMPREHENSIVE: All Features Integration Test" type="FAILURE">
<![CDATA[  [Microsoft Edge] › comprehensive-priority-tests.spec.ts:405:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:417:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting COMPREHENSIVE: All Features Integration Test

📝 Step 1: Complete authentication flow
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\test-failed-3.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-priority-tests.spec.ts" timestamp="2025-09-06T04:56:13.710Z" hostname="Google Chrome" tests="4" failures="4" skipped="0" time="9.12" errors="0">
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" classname="comprehensive-priority-tests.spec.ts" time="1.61">
<failure message="comprehensive-priority-tests.spec.ts:136:7 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption" type="FAILURE">
<![CDATA[  [Google Chrome] › comprehensive-priority-tests.spec.ts:136:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:163:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" classname="comprehensive-priority-tests.spec.ts" time="2.275">
<failure message="comprehensive-priority-tests.spec.ts:238:7 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)" type="FAILURE">
<![CDATA[  [Google Chrome] › comprehensive-priority-tests.spec.ts:238:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation) 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:251:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat

📝 Step 1: Login both users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\test-failed-3.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications" classname="comprehensive-priority-tests.spec.ts" time="3.057">
<failure message="comprehensive-priority-tests.spec.ts:300:7 🎯 PRIORITY 3: Group Chat Creation and Notifications" type="FAILURE">
<![CDATA[  [Google Chrome] › comprehensive-priority-tests.spec.ts:300:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 PRIORITY 3: Group Chat Creation and Notifications 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:315:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\test-failed-4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting PRIORITY 3: Group Chat Creation and Notifications

📝 Step 1: Login all three users
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\test-failed-2.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\test-failed-4.png]]
]]>
</system-out>
</testcase>
<testcase name="🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test" classname="comprehensive-priority-tests.spec.ts" time="2.178">
<failure message="comprehensive-priority-tests.spec.ts:405:7 🎯 COMPREHENSIVE: All Features Integration Test" type="FAILURE">
<![CDATA[  [Google Chrome] › comprehensive-priority-tests.spec.ts:405:7 › 🚀 Comprehensive Priority E2E Tests - Chat Application › 🎯 COMPREHENSIVE: All Features Integration Test 

    Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      29 | async function loginUser(page: Page, email: string, password: string) {
      30 |   console.log(`🔐 Logging in user: ${email}`);
    > 31 |   await page.goto(FRONTEND_URL);
         |              ^
      32 |   
      33 |   // Wait for login form to be visible
      34 |   await page.waitForSelector('form', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:31:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\comprehensive-priority-tests.spec.ts:417:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\test-failed-3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
🚀 Starting COMPREHENSIVE: All Features Integration Test

📝 Step 1: Complete authentication flow
🔐 Logging in user: <EMAIL>

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\test-failed-1.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\video.webm]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\test-failed-3.png]]

[[ATTACHMENT|..\test-results\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\test-failed-2.png]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>