// socket-server/src/tests/services/messageStatusService.test.ts
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mockDeep, mockReset, DeepMockProxy } from 'vitest-mock-extended'
import { PrismaClient } from '@prisma/client'
import { MessageStatusService } from '../../services/messageStatusService'
import { z } from 'zod'

describe('MessageStatusService', () => {
  let prismaMock: DeepMockProxy<PrismaClient>
  let messageStatusService: MessageStatusService

  const mockUser = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    username: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    profilePicture: null
  }

  const mockMessage = {
    id: '123e4567-e89b-12d3-a456-426614174003',
    conversationId: '123e4567-e89b-12d3-a456-426614174001',
    senderId: '123e4567-e89b-12d3-a456-426614174002',
    content: 'Hello world!',
    messageType: 'TEXT',
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
    conversation: {
      participants: [
        { userId: '123e4567-e89b-12d3-a456-426614174000' },
        { userId: '123e4567-e89b-12d3-a456-426614174002' }
      ]
    }
  }

  const mockMessageStatus = {
    id: '123e4567-e89b-12d3-a456-426614174004',
    messageId: '123e4567-e89b-12d3-a456-426614174003',
    userId: '123e4567-e89b-12d3-a456-426614174000',
    status: 'DELIVERED',
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
    user: mockUser
  }

  beforeEach(() => {
    prismaMock = mockDeep<PrismaClient>()
    messageStatusService = new MessageStatusService(prismaMock)
  })

  afterEach(() => {
    mockReset(prismaMock)
  })

  describe('updateMessageStatus', () => {
    it('should update message status successfully', async () => {
      prismaMock.message.findFirst.mockResolvedValue(mockMessage as any)
      prismaMock.messageStatus.upsert.mockResolvedValue(mockMessageStatus as any)

      const result = await messageStatusService.updateMessageStatus(
        mockMessage.id,
        mockUser.id,
        'DELIVERED'
      )

      expect(result).toEqual(mockMessageStatus)
      expect(prismaMock.messageStatus.upsert).toHaveBeenCalledWith({
        where: {
          messageId_userId: {
            messageId: mockMessage.id,
            userId: mockUser.id
          }
        },
        update: {
          status: 'DELIVERED',
          updatedAt: expect.any(Date)
        },
        create: {
          messageId: mockMessage.id,
          userId: mockUser.id,
          status: 'DELIVERED'
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true
            }
          }
        }
      })
    })

    it('should throw error if message not found', async () => {
      prismaMock.message.findFirst.mockResolvedValue(null)

      await expect(
        messageStatusService.updateMessageStatus(
          '123e4567-e89b-12d3-a456-************', // Valid UUID that doesn't exist
          mockUser.id,
          'DELIVERED'
        )
      ).rejects.toThrow('Message not found or access denied')
    })

    it('should throw error when trying to mark own message as read', async () => {
      const ownMessage = { ...mockMessage, senderId: mockUser.id }
      prismaMock.message.findFirst.mockResolvedValue(ownMessage as any)

      await expect(
        messageStatusService.updateMessageStatus(
          mockMessage.id,
          mockUser.id,
          'READ'
        )
      ).rejects.toThrow('Cannot mark own message as read')
    })

    it('should validate input data', async () => {
      await expect(
        messageStatusService.updateMessageStatus(
          'invalid-uuid',
          mockUser.id,
          'DELIVERED'
        )
      ).rejects.toThrow()
    })
  })

  describe('markMessageAsDelivered', () => {
    it('should call updateMessageStatus with DELIVERED status', async () => {
      prismaMock.message.findFirst.mockResolvedValue(mockMessage as any)
      prismaMock.messageStatus.upsert.mockResolvedValue(mockMessageStatus as any)

      const result = await messageStatusService.markMessageAsDelivered(
        mockMessage.id,
        mockUser.id
      )

      expect(result).toEqual(mockMessageStatus)
    })
  })

  describe('markMessageAsRead', () => {
    it('should call updateMessageStatus with READ status', async () => {
      const readStatus = { ...mockMessageStatus, status: 'READ' }
      prismaMock.message.findFirst.mockResolvedValue(mockMessage as any)
      prismaMock.messageStatus.upsert.mockResolvedValue(readStatus as any)

      const result = await messageStatusService.markMessageAsRead(
        mockMessage.id,
        mockUser.id
      )

      expect(result.status).toBe('READ')
    })
  })

  describe('markMessageAsFailed', () => {
    it('should call updateMessageStatus with FAILED status', async () => {
      const failedStatus = { ...mockMessageStatus, status: 'FAILED' }
      prismaMock.message.findFirst.mockResolvedValue(mockMessage as any)
      prismaMock.messageStatus.upsert.mockResolvedValue(failedStatus as any)

      const result = await messageStatusService.markMessageAsFailed(
        mockMessage.id,
        mockUser.id
      )

      expect(result.status).toBe('FAILED')
    })
  })

  describe('getMessageStatuses', () => {
    it('should return message statuses for authorized user', async () => {
      const statuses = [mockMessageStatus]
      prismaMock.message.findFirst.mockResolvedValue(mockMessage as any)
      prismaMock.messageStatus.findMany.mockResolvedValue(statuses as any)

      const result = await messageStatusService.getMessageStatuses(
        mockMessage.id,
        mockUser.id
      )

      expect(result).toEqual(statuses)
      expect(prismaMock.messageStatus.findMany).toHaveBeenCalledWith({
        where: { messageId: mockMessage.id },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true
            }
          }
        },
        orderBy: { updatedAt: 'desc' }
      })
    })

    it('should throw error for unauthorized user', async () => {
      prismaMock.message.findFirst.mockResolvedValue(null)

      await expect(
        messageStatusService.getMessageStatuses(
          mockMessage.id,
          'unauthorized-user-id'
        )
      ).rejects.toThrow('Message not found or access denied')
    })
  })

  describe('getConversationReadStatus', () => {
    it('should return read status for conversation messages', async () => {
      const messages = [
        {
          ...mockMessage,
          statuses: [{ status: 'READ', updatedAt: new Date() }]
        }
      ]
      prismaMock.message.findMany.mockResolvedValue(messages as any)

      const result = await messageStatusService.getConversationReadStatus(
        mockMessage.conversationId,
        mockUser.id
      )

      expect(result).toHaveLength(1)
      expect(result[0].status).toBe('READ')
      expect(prismaMock.message.findMany).toHaveBeenCalledWith({
        where: {
          conversationId: mockMessage.conversationId,
          NOT: { senderId: mockUser.id }
        },
        include: {
          statuses: {
            where: { userId: mockUser.id }
          }
        },
        orderBy: { createdAt: 'desc' }
      })
    })
  })
});
