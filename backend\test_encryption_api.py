#!/usr/bin/env python3
"""
Test script for Phase 3 Encryption API endpoints.
This script creates test users, generates cryptographic keys, and tests all encryption endpoints.
"""
import os
import sys
import django
import base64
import json
import requests
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import ec

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatapp.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

# Server configuration
BASE_URL = "http://127.0.0.1:8000"
API_BASE = f"{BASE_URL}/api"

class CryptoTestHelper:
    """Helper class for generating test cryptographic keys"""
    
    @staticmethod
    def generate_identity_keypair():
        """Generate ECDSA P-256 identity key pair"""
        private_key = ec.generate_private_key(ec.SECP256R1())
        public_key = private_key.public_key()
        
        # Export public key to SPKI format
        public_spki = public_key.public_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        return private_key, base64.b64encode(public_spki).decode('ascii')
    
    @staticmethod
    def generate_ecdh_keypair():
        """Generate ECDH P-256 key pair"""
        private_key = ec.generate_private_key(ec.SECP256R1())
        public_key = private_key.public_key()
        
        # Export public key to SPKI format
        public_spki = public_key.public_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        return private_key, base64.b64encode(public_spki).decode('ascii')
    
    @staticmethod
    def sign_prekey(identity_private_key, signed_prekey_spki_b64):
        """Sign the signed pre-key with identity private key"""
        signed_prekey_spki = base64.b64decode(signed_prekey_spki_b64)
        signature = identity_private_key.sign(
            signed_prekey_spki,
            ec.ECDSA(hashes.SHA256())
        )
        return base64.b64encode(signature).decode('ascii')

class EncryptionAPITester:
    """Test class for encryption API endpoints"""
    
    def __init__(self):
        self.crypto = CryptoTestHelper()
        self.test_users = {}
        self.auth_tokens = {}
        
    def setup_test_users(self):
        """Create test users and get authentication tokens"""
        print("🔧 Setting up test users...")
        
        # Create test users
        users_data = [
            {'username': 'alice_test', 'email': '<EMAIL>', 'password': 'testpass123'},
            {'username': 'bob_test', 'email': '<EMAIL>', 'password': 'testpass123'}
        ]
        
        for user_data in users_data:
            # Delete existing user if exists
            User.objects.filter(username=user_data['username']).delete()
            
            # Create new user
            user = User.objects.create_user(**user_data)
            self.test_users[user_data['username']] = user
            
            # Generate JWT token
            refresh = RefreshToken.for_user(user)
            self.auth_tokens[user_data['username']] = {
                'access': str(refresh.access_token),
                'refresh': str(refresh)
            }
            
            print(f"✅ Created user: {user_data['username']}")
        
        print(f"✅ Setup complete. Created {len(self.test_users)} test users.\n")
    
    def get_auth_headers(self, username):
        """Get authentication headers for a user"""
        token = self.auth_tokens[username]['access']
        return {'Authorization': f'Bearer {token}'}
    
    def test_upload_key_bundle(self, username):
        """Test key bundle upload endpoint"""
        print(f"🔑 Testing key bundle upload for {username}...")
        
        # Generate cryptographic keys
        identity_private, identity_public_b64 = self.crypto.generate_identity_keypair()
        signed_prekey_private, signed_prekey_public_b64 = self.crypto.generate_ecdh_keypair()
        
        # Sign the signed pre-key
        signature_b64 = self.crypto.sign_prekey(identity_private, signed_prekey_public_b64)
        
        # Prepare request data
        data = {
            'identity_public_key': identity_public_b64,
            'signed_prekey_id': 1,
            'signed_prekey_public': signed_prekey_public_b64,
            'signed_prekey_signature': signature_b64
        }
        
        # Make request
        response = requests.post(
            f"{API_BASE}/encryption/bundles/",
            json=data,
            headers=self.get_auth_headers(username)
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 201:
            print(f"✅ Key bundle upload successful for {username}")
            return True
        else:
            print(f"❌ Key bundle upload failed for {username}")
            return False
    
    def test_upload_one_time_prekeys(self, username, count=5):
        """Test one-time pre-keys upload endpoint"""
        print(f"🔐 Testing one-time pre-keys upload for {username} ({count} keys)...")
        
        # Generate multiple one-time pre-keys
        prekeys = []
        for i in range(count):
            _, public_key_b64 = self.crypto.generate_ecdh_keypair()
            prekeys.append({
                'key_id': i + 1,
                'public_key': public_key_b64
            })
        
        data = {'prekeys': prekeys}
        
        # Make request
        response = requests.post(
            f"{API_BASE}/encryption/prekeys/",
            json=data,
            headers=self.get_auth_headers(username)
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 201:
            print(f"✅ One-time pre-keys upload successful for {username}")
            return True
        else:
            print(f"❌ One-time pre-keys upload failed for {username}")
            return False
    
    def test_get_prekey_count(self, username):
        """Test pre-key count endpoint"""
        print(f"📊 Testing pre-key count for {username}...")
        
        response = requests.get(
            f"{API_BASE}/encryption/prekeys/count/",
            headers=self.get_auth_headers(username)
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 200:
            print(f"✅ Pre-key count retrieval successful for {username}")
            return True
        else:
            print(f"❌ Pre-key count retrieval failed for {username}")
            return False
    
    def test_get_key_bundle(self, requester, target):
        """Test key bundle retrieval endpoint"""
        print(f"🔍 Testing key bundle retrieval: {requester} requesting {target}'s bundle...")
        
        target_user = self.test_users[target]
        
        response = requests.get(
            f"{API_BASE}/encryption/bundles/{target_user.id}/",
            headers=self.get_auth_headers(requester)
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 200:
            print(f"✅ Key bundle retrieval successful")
            return True
        else:
            print(f"❌ Key bundle retrieval failed")
            return False
    
    def test_error_scenarios(self):
        """Test various error scenarios"""
        print("🚨 Testing error scenarios...")
        
        # Test without authentication
        print("   Testing without authentication...")
        response = requests.get(f"{API_BASE}/encryption/prekeys/count/")
        print(f"   Status: {response.status_code} (expected: 401)")
        
        # Test invalid signature
        print("   Testing invalid signature...")
        identity_private, identity_public_b64 = self.crypto.generate_identity_keypair()
        _, signed_prekey_public_b64 = self.crypto.generate_ecdh_keypair()
        
        data = {
            'identity_public_key': identity_public_b64,
            'signed_prekey_id': 999,
            'signed_prekey_public': signed_prekey_public_b64,
            'signed_prekey_signature': base64.b64encode(b'invalid_signature').decode('ascii')
        }
        
        response = requests.post(
            f"{API_BASE}/encryption/bundles/",
            json=data,
            headers=self.get_auth_headers('alice_test')
        )
        print(f"   Status: {response.status_code} (expected: 400)")
        print(f"   Response: {response.json()}")
        
        print("✅ Error scenario testing complete\n")
    
    def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting Phase 3 Encryption API Tests\n")
        
        # Setup
        self.setup_test_users()
        
        # Test each user's endpoints
        for username in self.test_users.keys():
            print(f"📋 Testing endpoints for {username}:")
            self.test_upload_key_bundle(username)
            self.test_upload_one_time_prekeys(username)
            self.test_get_prekey_count(username)
            print()
        
        # Test cross-user operations
        print("🔄 Testing cross-user operations:")
        self.test_get_key_bundle('alice_test', 'bob_test')
        self.test_get_key_bundle('bob_test', 'alice_test')
        print()
        
        # Test error scenarios
        self.test_error_scenarios()
        
        print("🎉 All tests completed!")

if __name__ == "__main__":
    tester = EncryptionAPITester()
    tester.run_all_tests()
