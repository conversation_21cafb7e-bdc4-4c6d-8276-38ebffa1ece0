{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/vite": "^4.1.11", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.7.0", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.11", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@playwright/test": "^1.55.0", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.0.0", "msw": "^2.8.4", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^5.4.19", "vitest": "^2.1.8", "vitest-mock-extended": "^2.0.2"}}