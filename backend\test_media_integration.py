#!/usr/bin/env python3
"""
Test script for Phase 5 Media Sharing functionality
Tests the media upload/download APIs and integration with the existing chat system
"""

import os
import sys
import django
import requests
import json
import base64
import tempfile
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatapp.settings')
django.setup()

from django.contrib.auth import get_user_model
from messaging.models import Conversation, ConversationParticipant, Message
from media.models import MediaFile, MediaDownload, MediaChunk

User = get_user_model()

# API Configuration
BASE_URL = 'http://localhost:6000/api'
HEADERS = {'Content-Type': 'application/json'}

class MediaIntegrationTester:
    def __init__(self):
        self.access_token = None
        self.test_users = []
        self.test_conversation = None
        self.test_message = None
        self.test_media_file = None

    def setup_test_data(self):
        """Create test users and conversation"""
        print("Setting up test data...")
        
        # Create test users
        try:
            user1 = User.objects.get(email='<EMAIL>')
        except User.DoesNotExist:
            user1 = User.objects.create_user(
                username='mediatest1',
                email='<EMAIL>',
                password='testpass123',
                first_name='Media',
                last_name='Tester1'
            )
        
        try:
            user2 = User.objects.get(email='<EMAIL>')
        except User.DoesNotExist:
            user2 = User.objects.create_user(
                username='mediatest2',
                email='<EMAIL>',
                password='testpass123',
                first_name='Media',
                last_name='Tester2'
            )
        
        self.test_users = [user1, user2]
        
        # Create test conversation
        conversation, created = Conversation.objects.get_or_create(
            type='DIRECT',
            defaults={'name': 'Media Test Conversation'}
        )
        self.test_conversation = conversation
        
        # Add participants
        for user in self.test_users:
            ConversationParticipant.objects.get_or_create(
                conversation=conversation,
                user=user,
                defaults={'is_active': True}
            )
        
        # Create test message
        message, created = Message.objects.get_or_create(
            conversation=conversation,
            sender=user1,
            defaults={
                'content': 'Test message for media upload',
                'message_type': 'TEXT'
            }
        )
        self.test_message = message
        
        print(f"[OK] Created test conversation: {conversation.id}")
        print(f"[OK] Created test message: {message.id}")

    def authenticate(self):
        """Authenticate and get access token"""
        print("Authenticating...")
        
        auth_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        try:
            response = requests.post(
                f'{BASE_URL}/auth/login/',
                json=auth_data,
                headers=HEADERS
            )
            
            if response.status_code == 200:
                data = response.json()
                # Handle different response formats
                if 'data' in data and 'tokens' in data['data']:
                    self.access_token = data['data']['tokens']['access']
                elif 'tokens' in data:
                    self.access_token = data['tokens']['access']
                elif 'access' in data:
                    self.access_token = data['access']
                else:
                    print(f"[FAIL] Unexpected auth response format: {data}")
                    return False
                print("[OK] Authentication successful")
                return True
            else:
                print(f"[FAIL] Authentication failed: {response.status_code}")
                print(response.text)
                return False

        except requests.exceptions.ConnectionError:
            print("[FAIL] Cannot connect to server. Make sure Django server is running on port 6000")
            return False

    def test_simple_media_upload(self):
        """Test simple media upload for small files"""
        print("\nTesting simple media upload...")
        
        if not self.access_token:
            print("[FAIL] No access token available")
            return False
        
        # Create a test file
        test_content = b"This is a test file content for media upload testing."
        
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name
        
        try:
            # Prepare upload data
            upload_data = {
                'message_id': str(self.test_message.id),
                'wrapped_file_key': base64.b64encode(b'dummy_wrapped_key_32_bytes_long').decode(),
                'file_nonce': base64.b64encode(b'dummy_nonce_12').decode(),
                'file_hash': 'dummy_hash_placeholder'
            }
            
            files = {
                'file': ('test_file.txt', open(temp_file_path, 'rb'), 'text/plain')
            }
            
            headers = {
                'Authorization': f'Bearer {self.access_token}'
            }
            
            response = requests.post(
                f'{BASE_URL}/media/upload/simple/',
                data=upload_data,
                files=files,
                headers=headers
            )
            
            files['file'][1].close()  # Close the file
            
            if response.status_code == 201:
                data = response.json()
                self.test_media_file = data
                print(f"[OK] Simple upload successful: {data['id']}")
                print(f"  - Filename: {data['original_filename']}")
                print(f"  - File type: {data['file_type']}")
                print(f"  - File size: {data['file_size']} bytes")
                return True
            else:
                print(f"[FAIL] Simple upload failed: {response.status_code}")
                print(response.text)
                return False

        except Exception as e:
            print(f"[FAIL] Simple upload error: {str(e)}")
            return False
        finally:
            # Clean up temp file
            try:
                os.unlink(temp_file_path)
            except:
                pass

    def test_media_download(self):
        """Test media download functionality"""
        print("\nTesting media download...")
        
        if not self.test_media_file:
            print("[FAIL] No media file available for download test")
            return False
        
        try:
            headers = {
                'Authorization': f'Bearer {self.access_token}'
            }
            
            # Request download token
            response = requests.get(
                f'{BASE_URL}/media/download/{self.test_media_file["id"]}/',
                headers=headers
            )
            
            if response.status_code == 200:
                download_data = response.json()
                print(f"[OK] Download token generated")
                print(f"  - Download URL: {download_data['download_url']}")
                print(f"  - Expires at: {download_data['expires_at']}")

                # Test actual download
                download_token = download_data['download_url'].split('/')[-2]
                download_response = requests.get(
                    f'{BASE_URL}/media/download/{download_token}/',
                    headers=headers  # Pass auth headers for secure download
                )

                if download_response.status_code == 200:
                    print(f"[OK] File download successful")
                    print(f"  - Content length: {len(download_response.content)} bytes")
                    return True
                else:
                    print(f"[FAIL] File download failed: {download_response.status_code}")
                    return False
            else:
                print(f"[FAIL] Download token generation failed: {response.status_code}")
                print(response.text)
                return False

        except Exception as e:
            print(f"[FAIL] Download test error: {str(e)}")
            return False

    def test_database_integration(self):
        """Test database integration and signal handling"""
        print("\nTesting database integration...")
        
        try:
            # Check if message has_media flag was updated
            message = Message.objects.get(id=self.test_message.id)
            
            if message.has_media and message.media_count > 0:
                print(f"[OK] Message media flags updated correctly")
                print(f"  - has_media: {message.has_media}")
                print(f"  - media_count: {message.media_count}")
            else:
                print(f"[FAIL] Message media flags not updated")
                print(f"  - has_media: {message.has_media}")
                print(f"  - media_count: {message.media_count}")
                return False

            # Check media file in database
            media_files = MediaFile.objects.filter(message=message)
            if media_files.exists():
                media_file = media_files.first()
                print(f"[OK] Media file stored in database")
                print(f"  - ID: {media_file.id}")
                print(f"  - Processing status: {media_file.processing_status}")
                print(f"  - Virus scan status: {media_file.virus_scan_status}")
                return True
            else:
                print(f"[FAIL] Media file not found in database")
                return False

        except Exception as e:
            print(f"[FAIL] Database integration test error: {str(e)}")
            return False

    def run_all_tests(self):
        """Run all media integration tests"""
        print("=" * 60)
        print("PHASE 5 MEDIA SHARING INTEGRATION TESTS")
        print("=" * 60)
        
        # Setup
        self.setup_test_data()
        
        if not self.authenticate():
            print("\n[FAIL] Cannot proceed without authentication")
            return False
        
        # Run tests
        tests = [
            self.test_simple_media_upload,
            self.test_media_download,
            self.test_database_integration,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        # Summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        print(f"Passed: {passed}/{total}")
        
        if passed == total:
            print("[OK] All media integration tests passed!")
            return True
        else:
            print(f"[FAIL] {total - passed} test(s) failed")
            return False

if __name__ == '__main__':
    tester = MediaIntegrationTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
