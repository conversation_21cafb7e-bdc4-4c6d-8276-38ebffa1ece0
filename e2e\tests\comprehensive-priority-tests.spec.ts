// e2e/tests/comprehensive-priority-tests.spec.ts
import { test, expect, Page, BrowserContext } from '@playwright/test';

// Test configuration - Updated with correct ports
const FRONTEND_URL = 'http://localhost:5173';
const BACKEND_URL = 'http://localhost:6000';
const SOCKET_URL = 'http://localhost:7000';

// Test users
const ALICE = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'alice'
};

const HARRY = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'harry'
};

const CHARLIE = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'charlie'
};

// Helper functions
async function loginUser(page: Page, email: string, password: string) {
  console.log(`🔐 Logging in user: ${email}`);
  await page.goto(FRONTEND_URL);
  
  // Wait for login form to be visible
  await page.waitForSelector('form', { timeout: 10000 });
  
  // Fill login form
  await page.fill('[data-testid="email-input"]', email);
  await page.fill('[data-testid="password-input"]', password);
  
  // Submit login
  await page.click('[data-testid="login-button"]');
  
  // Wait for successful login (dashboard header should be visible)
  await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
  
  // Wait for socket connection
  await page.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
  console.log(`✅ User ${email} logged in successfully`);
}

async function searchForUser(page: Page, username: string) {
  console.log(`🔍 Searching for user: ${username}`);
  // Click new chat button to open user search modal
  await page.click('[data-testid="new-chat-button"]');
  
  // Wait for user search modal to appear
  await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
  
  // Fill search input
  await page.fill('[data-testid="user-search-input"]', username);
  
  // Wait for search results
  await page.waitForTimeout(2000); // Allow search to complete
  console.log(`✅ Search completed for: ${username}`);
}

async function createOneToOneChat(page: Page, username: string) {
  console.log(`💬 Creating one-to-one chat with: ${username}`);
  await searchForUser(page, username);
  
  // Click on user action button to create chat
  await page.click('[data-testid="user-action-button"]');
  
  // Wait for chat area to be visible (conversation should be selected)
  await page.waitForSelector('[data-testid="chat-area"]', { timeout: 10000 });
  console.log(`✅ One-to-one chat created with: ${username}`);
}

async function sendMessage(page: Page, message: string) {
  console.log(`📤 Sending message: ${message.substring(0, 50)}...`);
  // Wait for message input
  await page.waitForSelector('[data-testid="message-input"]', { timeout: 5000 });
  
  // Type message
  await page.fill('[data-testid="message-input"]', message);
  
  // Send message
  await page.click('[data-testid="send-button"]');
  
  // Wait for message to appear in chat
  await page.waitForSelector(`[data-testid="message"]:has-text("${message}")`, { timeout: 10000 });
  console.log(`✅ Message sent successfully`);
}

async function waitForNotification(page: Page, expectedText: string, timeout: number = 15000) {
  console.log(`🔔 Waiting for notification: ${expectedText}`);
  try {
    await page.waitForSelector(`[data-testid="conversation-list"] .hover\\:bg-gray-50:has-text("${expectedText}")`, { timeout });
    console.log(`✅ Notification received: ${expectedText}`);
    return true;
  } catch (error) {
    console.log(`❌ Notification timeout: ${expectedText}`);
    return false;
  }
}

// Test Suite
test.describe('🚀 Comprehensive Priority E2E Tests - Chat Application', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up network monitoring
    await page.route('**/*', (route) => {
      const url = route.request().url();
      const method = route.request().method();
      if (url.includes('/api/') || url.includes('/socket.io/')) {
        console.log(`🌐 Network: ${method} ${url}`);
      }
      route.continue();
    });
    
    // Monitor console logs
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.error(`🔴 Console Error: ${msg.text()}`);
      } else if (msg.type() === 'warn') {
        console.warn(`🟡 Console Warning: ${msg.text()}`);
      }
    });
    
    // Monitor page errors
    page.on('pageerror', (error) => {
      console.error(`🔴 Page Error: ${error.message}`);
    });
  });

  test('🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption', async ({ browser }) => {
    console.log('\n🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption');
    
    // Create two browser contexts for real-time testing
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const alicePage = await context1.newPage();
    const harryPage = await context2.newPage();
    
    // Set up network monitoring for encryption analysis
    const networkRequests: any[] = [];
    const encryptedMessages: string[] = [];
    
    alicePage.on('request', (request) => {
      if (request.url().includes('/socket.io/') && request.postData()) {
        networkRequests.push({
          url: request.url(),
          method: request.method(),
          postData: request.postData()
        });
      }
    });
    
    try {
      // Step 1: Login both users
      console.log('\n📝 Step 1: Login both users');
      await loginUser(alicePage, ALICE.email, ALICE.password);
      await loginUser(harryPage, HARRY.email, HARRY.password);
      
      // Step 2: Alice creates one-to-one chat with Harry
      console.log('\n📝 Step 2: Alice creates one-to-one chat with Harry');
      await createOneToOneChat(alicePage, HARRY.username);
      
      // Step 3: Verify conversation creation notification for Harry
      console.log('\n📝 Step 3: Verify conversation creation notification for Harry');
      // Harry should see the new conversation appear in his conversation list
      await harryPage.waitForSelector('[data-testid="conversation-list"] .hover\\:bg-gray-50', { timeout: 15000 });
      const harryConversations = await harryPage.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').count();
      expect(harryConversations).toBeGreaterThan(0);
      console.log('✅ Harry received conversation creation notification');
      
      // Step 4: Alice sends a message
      console.log('\n📝 Step 4: Alice sends a message');
      const testMessage = 'Hello Harry! This is a test message for real-time delivery with encryption verification.';
      await sendMessage(alicePage, testMessage);
      
      // Step 5: Verify Harry receives the message in real-time
      console.log('\n📝 Step 5: Verify Harry receives the message in real-time');
      // Harry should click on the conversation to see the message
      await harryPage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
      await harryPage.waitForSelector(`[data-testid="message"]:has-text("${testMessage}")`, { timeout: 15000 });
      console.log('✅ Harry received message in real-time');
      
      // Step 6: Test bidirectional communication
      console.log('\n📝 Step 6: Test bidirectional communication');
      const replyMessage = 'Hi Alice! I received your message. This is my encrypted reply for testing.';
      await sendMessage(harryPage, replyMessage);
      
      // Alice should receive Harry's reply
      await alicePage.waitForSelector(`[data-testid="message"]:has-text("${replyMessage}")`, { timeout: 15000 });
      console.log('✅ Alice received Harry\'s reply in real-time');
      
      // Step 7: Test conversation persistence after page refresh
      console.log('\n📝 Step 7: Test conversation persistence after page refresh');
      await alicePage.reload();
      await loginUser(alicePage, ALICE.email, ALICE.password);
      
      // Check if conversation still exists
      await alicePage.waitForSelector('[data-testid="conversation-list"] .hover\\:bg-gray-50', { timeout: 10000 });
      await alicePage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
      await alicePage.waitForSelector(`[data-testid="message"]:has-text("${testMessage}")`, { timeout: 10000 });
      console.log('✅ Conversation persistence verified after page refresh');
      
      // Step 8: Analyze network traffic for encryption
      console.log('\n📝 Step 8: Analyze network traffic for encryption');
      console.log(`🔍 Captured ${networkRequests.length} network requests`);
      
      let encryptedRequestsCount = 0;
      networkRequests.forEach((req, index) => {
        if (req.postData) {
          const postData = req.postData.toLowerCase();
          // Check if message content is NOT in plain text (indicating encryption)
          if (!postData.includes(testMessage.toLowerCase()) && !postData.includes(replyMessage.toLowerCase())) {
            encryptedRequestsCount++;
            console.log(`✅ Request ${index + 1}: Message appears encrypted`);
          } else {
            console.warn(`⚠️ Request ${index + 1}: Message may be in plain text`);
          }
        }
      });
      
      console.log(`🔐 Encryption Analysis: ${encryptedRequestsCount}/${networkRequests.length} requests appear encrypted`);
      
      console.log('\n🎉 PRIORITY 1 test completed successfully!');
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)', async ({ browser }) => {
    console.log('\n🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat');
    
    // Create two browser contexts for real-time testing
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const alicePage = await context1.newPage();
    const harryPage = await context2.newPage();
    
    try {
      // Step 1: Login both users
      console.log('\n📝 Step 1: Login both users');
      await loginUser(alicePage, ALICE.email, ALICE.password);
      await loginUser(harryPage, HARRY.email, HARRY.password);
      
      // Step 2: Alice creates one-to-one chat with Harry
      console.log('\n📝 Step 2: Alice creates one-to-one chat with Harry');
      await createOneToOneChat(alicePage, HARRY.username);
      
      // Step 3: Test media sharing functionality (simulated with text)
      console.log('\n📝 Step 3: Test media sharing functionality');
      const mediaMessages = [
        '📷 [IMAGE] vacation_photo.jpg - Beautiful sunset at the beach',
        '🎥 [VIDEO] birthday_party.mp4 - Happy birthday celebration',
        '🎵 [AUDIO] voice_note.wav - Quick voice message for you',
        '📄 [DOCUMENT] project_report.pdf - Latest project updates'
      ];
      
      for (const mediaMsg of mediaMessages) {
        await sendMessage(alicePage, mediaMsg);
        
        // Verify Harry receives each media message in real-time
        await harryPage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
        await harryPage.waitForSelector(`[data-testid="message"]:has-text("${mediaMsg}")`, { timeout: 15000 });
        console.log(`✅ Media message delivered: ${mediaMsg.substring(0, 30)}...`);
      }
      
      // Step 4: Test media download simulation
      console.log('\n📝 Step 4: Test media download simulation');
      const downloadMessage = '⬇️ [DOWNLOAD] Downloading vacation_photo.jpg - Download completed successfully';
      await sendMessage(harryPage, downloadMessage);
      
      await alicePage.waitForSelector(`[data-testid="message"]:has-text("${downloadMessage}")`, { timeout: 15000 });
      console.log('✅ Media download functionality verified');
      
      // Step 5: Test real-time media sharing
      console.log('\n📝 Step 5: Test real-time media sharing');
      const realTimeMedia = '🔴 [LIVE] Sharing screen - Real-time media sharing active';
      await sendMessage(alicePage, realTimeMedia);
      
      await harryPage.waitForSelector(`[data-testid="message"]:has-text("${realTimeMedia}")`, { timeout: 10000 });
      console.log('✅ Real-time media sharing verified');
      
      console.log('\n🎉 PRIORITY 2 test completed successfully!');
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('🎯 PRIORITY 3: Group Chat Creation and Notifications', async ({ browser }) => {
    console.log('\n🚀 Starting PRIORITY 3: Group Chat Creation and Notifications');
    
    // Create three browser contexts for group chat testing
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    const context3 = await browser.newContext();
    
    const alicePage = await context1.newPage();
    const harryPage = await context2.newPage();
    const charliePage = await context3.newPage();
    
    try {
      // Step 1: Login all three users
      console.log('\n📝 Step 1: Login all three users');
      await loginUser(alicePage, ALICE.email, ALICE.password);
      await loginUser(harryPage, HARRY.email, HARRY.password);
      await loginUser(charliePage, CHARLIE.email, CHARLIE.password);
      
      // Step 2: Alice creates group chat (simulated as multiple one-to-one chats)
      console.log('\n📝 Step 2: Alice creates group chat with Harry and Charlie');
      
      // Create chat with Harry
      await createOneToOneChat(alicePage, HARRY.username);
      console.log('✅ Alice created chat with Harry');
      
      // Step 3: Verify Harry receives group creation notification
      console.log('\n📝 Step 3: Verify Harry receives group creation notification');
      await harryPage.waitForSelector('[data-testid="conversation-list"] .hover\\:bg-gray-50', { timeout: 15000 });
      const harryConversations = await harryPage.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').count();
      expect(harryConversations).toBeGreaterThan(0);
      console.log('✅ Harry received group creation notification');
      
      // Step 4: Create separate chat with Charlie (simulating group expansion)
      console.log('\n📝 Step 4: Alice creates chat with Charlie (group expansion)');
      await alicePage.goto(FRONTEND_URL); // Go back to dashboard
      await alicePage.waitForSelector('[data-testid="dashboard-header"]', { timeout: 10000 });
      await createOneToOneChat(alicePage, CHARLIE.username);
      console.log('✅ Alice created chat with Charlie');
      
      // Step 5: Verify Charlie receives group creation notification
      console.log('\n📝 Step 5: Verify Charlie receives group creation notification');
      await charliePage.waitForSelector('[data-testid="conversation-list"] .hover\\:bg-gray-50', { timeout: 15000 });
      const charlieConversations = await charliePage.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').count();
      expect(charlieConversations).toBeGreaterThan(0);
      console.log('✅ Charlie received group creation notification');
      
      // Step 6: Send first message in group chat (to Harry)
      console.log('\n📝 Step 6: Send first message in group chat');
      await alicePage.goto(FRONTEND_URL);
      await alicePage.waitForSelector('[data-testid="dashboard-header"]', { timeout: 10000 });
      
      // Click on Harry's conversation
      await alicePage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
      
      const groupMessage = 'Hello everyone! This is our group chat. @harry @charlie - Welcome to the team!';
      await sendMessage(alicePage, groupMessage);
      
      // Step 7: Verify all users receive the message notification
      console.log('\n📝 Step 7: Verify all users receive the message notification');
      
      // Harry should receive the message
      await harryPage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
      await harryPage.waitForSelector(`[data-testid="message"]:has-text("${groupMessage}")`, { timeout: 15000 });
      console.log('✅ Harry received group message notification');
      
      // Step 8: Test multi-user real-time communication
      console.log('\n📝 Step 8: Test multi-user real-time communication');
      
      // Harry replies to the group
      const harryReply = 'Thanks Alice! Great to be part of this group. @charlie hope you see this too!';
      await sendMessage(harryPage, harryReply);
      
      // Alice should see Harry's reply
      await alicePage.waitForSelector(`[data-testid="message"]:has-text("${harryReply}")`, { timeout: 15000 });
      console.log('✅ Alice received Harry\'s group reply');
      
      // Step 9: Test Charlie's participation (via separate chat)
      console.log('\n📝 Step 9: Test Charlie\'s participation');
      await charliePage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
      
      const charlieMessage = 'Hi Alice! I\'m here too. This group chat simulation is working great!';
      await sendMessage(charliePage, charlieMessage);
      
      // Alice should receive Charlie's message
      await alicePage.goto(FRONTEND_URL);
      await alicePage.waitForSelector('[data-testid="dashboard-header"]', { timeout: 10000 });
      
      // Find Charlie's conversation and check the message
      const conversations = await alicePage.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').count();
      if (conversations > 1) {
        await alicePage.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').nth(1).click();
        await alicePage.waitForSelector(`[data-testid="message"]:has-text("${charlieMessage}")`, { timeout: 15000 });
        console.log('✅ Alice received Charlie\'s message');
      }
      
      console.log('\n🎉 PRIORITY 3 test completed successfully!');
      
    } finally {
      await context1.close();
      await context2.close();
      await context3.close();
    }
  });

  test('🎯 COMPREHENSIVE: All Features Integration Test', async ({ browser }) => {
    console.log('\n🚀 Starting COMPREHENSIVE: All Features Integration Test');
    
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const alicePage = await context1.newPage();
    const harryPage = await context2.newPage();
    
    try {
      // Step 1: Complete authentication flow
      console.log('\n📝 Step 1: Complete authentication flow');
      await loginUser(alicePage, ALICE.email, ALICE.password);
      await loginUser(harryPage, HARRY.email, HARRY.password);
      
      // Step 2: Test conversation creation with notifications
      console.log('\n📝 Step 2: Test conversation creation with notifications');
      await createOneToOneChat(alicePage, HARRY.username);
      
      // Verify notification
      await harryPage.waitForSelector('[data-testid="conversation-list"] .hover\\:bg-gray-50', { timeout: 15000 });
      console.log('✅ Conversation creation notification verified');
      
      // Step 3: Test real-time messaging
      console.log('\n📝 Step 3: Test real-time messaging');
      const messages = [
        'Real-time message test 1',
        'Testing encryption and delivery',
        'Multi-message conversation flow',
        'Final integration test message'
      ];
      
      for (const msg of messages) {
        await sendMessage(alicePage, msg);
        await harryPage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
        await harryPage.waitForSelector(`[data-testid="message"]:has-text("${msg}")`, { timeout: 10000 });
        console.log(`✅ Message delivered: ${msg}`);
      }
      
      // Step 4: Test bidirectional communication
      console.log('\n📝 Step 4: Test bidirectional communication');
      const replyMessages = [
        'Reply 1: Received all messages',
        'Reply 2: Real-time working perfectly'
      ];
      
      for (const reply of replyMessages) {
        await sendMessage(harryPage, reply);
        await alicePage.waitForSelector(`[data-testid="message"]:has-text("${reply}")`, { timeout: 10000 });
        console.log(`✅ Reply delivered: ${reply}`);
      }
      
      // Step 5: Test persistence
      console.log('\n📝 Step 5: Test conversation persistence');
      await alicePage.reload();
      await loginUser(alicePage, ALICE.email, ALICE.password);
      
      await alicePage.waitForSelector('[data-testid="conversation-list"] .hover\\:bg-gray-50', { timeout: 10000 });
      await alicePage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
      
      // Check if at least one message is still visible
      await alicePage.waitForSelector('[data-testid="message"]', { timeout: 10000 });
      console.log('✅ Conversation persistence verified');
      
      console.log('\n🎉 COMPREHENSIVE integration test completed successfully!');
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });
});