# backend/tests/test_api_messaging.py
import pytest
import json
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from messaging.models import Conversation, ConversationParticipant, Message
from tests.factories import (
    UserFactory, ConversationFactory, GroupConversationFactory,
    ConversationParticipantFactory, MessageFactory
)

User = get_user_model()

@pytest.mark.django_db
class TestConversationListAPI:
    """Test conversation list API endpoint."""
    
    def test_list_conversations_authenticated(self, authenticated_client, user):
        """Test listing conversations for authenticated user."""
        # Create conversations where user participates
        conv1 = ConversationFactory()
        conv2 = GroupConversationFactory()
        conv3 = ConversationFactory()  # User doesn't participate
        
        ConversationParticipantFactory(user=user, conversation=conv1)
        ConversationParticipantFactory(user=user, conversation=conv2)
        
        other_user = UserFactory()
        ConversationParticipantFactory(user=other_user, conversation=conv3)
        
        url = reverse('conversation-list')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert 'results' in data
        assert len(data['results']) == 2
        
        # Check that conversations are ordered by updated_at descending
        conversation_ids = [conv['id'] for conv in data['results']]
        assert str(conv2.id) in conversation_ids
        assert str(conv1.id) in conversation_ids
        assert str(conv3.id) not in conversation_ids
    
    def test_list_conversations_unauthenticated(self, api_client):
        """Test listing conversations without authentication."""
        url = reverse('conversation-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_list_conversations_with_pagination(self, authenticated_client, user):
        """Test conversation list with pagination."""
        # Create many conversations
        conversations = []
        for i in range(25):
            conv = ConversationFactory()
            ConversationParticipantFactory(user=user, conversation=conv)
            conversations.append(conv)
        
        url = reverse('conversation-list')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert 'results' in data
        assert 'count' in data
        assert 'next' in data
        assert 'previous' in data
        
        # Default page size should be 50, so all should be on first page
        assert len(data['results']) == 25
        assert data['count'] == 25
    
    def test_conversation_list_response_structure(self, authenticated_client, user):
        """Test conversation list response structure."""
        other_user = UserFactory()
        conversation = ConversationFactory()
        
        ConversationParticipantFactory(user=user, conversation=conversation)
        ConversationParticipantFactory(user=other_user, conversation=conversation)
        
        # Add a message
        message = MessageFactory(conversation=conversation, sender=other_user)
        
        url = reverse('conversation-list')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        conv_data = data['results'][0]
        
        # Check required fields
        required_fields = [
            'id', 'type', 'name', 'participants', 
            'last_message', 'created_at', 'updated_at'
        ]
        for field in required_fields:
            assert field in conv_data
        
        # Check participants structure
        assert len(conv_data['participants']) == 2
        participant = conv_data['participants'][0]
        participant_fields = ['id', 'username', 'first_name', 'last_name', 'profile_picture']
        for field in participant_fields:
            assert field in participant
        
        # Check last message structure
        last_msg = conv_data['last_message']
        assert last_msg is not None
        message_fields = [
            'id', 'conversation_id', 'sender', 'content', 
            'message_type', 'created_at', 'updated_at'
        ]
        for field in message_fields:
            assert field in last_msg

@pytest.mark.django_db
class TestCreateConversationAPI:
    """Test create conversation API endpoint."""
    
    def test_create_direct_conversation(self, authenticated_client, user):
        """Test creating a direct conversation."""
        other_user = UserFactory()
        
        conversation_data = {
            'type': 'DIRECT',
            'participant_ids': [str(other_user.id)]
        }
        
        url = reverse('create-conversation')
        response = authenticated_client.post(url, conversation_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        
        data = response.json()
        assert data['type'] == 'DIRECT'
        assert data['name'] is None
        assert len(data['participants']) == 2
        
        # Verify conversation was created in database
        conversation = Conversation.objects.get(id=data['id'])
        assert conversation.type == 'DIRECT'
        assert conversation.participants.count() == 2
        
        # Verify both users are participants
        participant_users = [p.user for p in conversation.participants.all()]
        assert user in participant_users
        assert other_user in participant_users
    
    def test_create_group_conversation(self, authenticated_client, user):
        """Test creating a group conversation."""
        other_users = UserFactory.create_batch(2)
        
        conversation_data = {
            'type': 'GROUP',
            'name': 'Test Group',
            'participant_ids': [str(u.id) for u in other_users]
        }
        
        url = reverse('create-conversation')
        response = authenticated_client.post(url, conversation_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        
        data = response.json()
        assert data['type'] == 'GROUP'
        assert data['name'] == 'Test Group'
        assert len(data['participants']) == 3  # Creator + 2 others
        
        # Verify conversation was created in database
        conversation = Conversation.objects.get(id=data['id'])
        assert conversation.type == 'GROUP'
        assert conversation.name == 'Test Group'
        assert conversation.participants.count() == 3
        
        # Verify creator has ADMIN role
        creator_participant = conversation.participants.get(user=user)
        assert creator_participant.role == 'ADMIN'
    
    def test_create_conversation_duplicate_direct(self, authenticated_client, user):
        """Test creating duplicate direct conversation returns existing one."""
        other_user = UserFactory()
        
        # Create existing conversation
        existing_conv = ConversationFactory(type='DIRECT')
        ConversationParticipantFactory(user=user, conversation=existing_conv)
        ConversationParticipantFactory(user=other_user, conversation=existing_conv)
        
        conversation_data = {
            'type': 'DIRECT',
            'participant_ids': [str(other_user.id)]
        }
        
        url = reverse('create-conversation')
        response = authenticated_client.post(url, conversation_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data['id'] == str(existing_conv.id)
        
        # Verify no new conversation was created
        assert Conversation.objects.filter(type='DIRECT').count() == 1
    
    def test_create_conversation_validation_errors(self, authenticated_client, user):
        """Test create conversation with validation errors."""
        # Missing required fields
        response = authenticated_client.post(
            reverse('create-conversation'), {}, format='json'
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        # Group conversation without name
        conversation_data = {
            'type': 'GROUP',
            'participant_ids': [str(UserFactory().id)]
        }
        
        response = authenticated_client.post(
            reverse('create-conversation'), conversation_data, format='json'
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        # Invalid participant IDs
        conversation_data = {
            'type': 'DIRECT',
            'participant_ids': ['invalid-uuid']
        }
        
        response = authenticated_client.post(
            reverse('create-conversation'), conversation_data, format='json'
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_create_conversation_nonexistent_participants(self, authenticated_client, user):
        """Test creating conversation with nonexistent participant IDs."""
        import uuid
        
        conversation_data = {
            'type': 'DIRECT',
            'participant_ids': [str(uuid.uuid4())]
        }
        
        url = reverse('create-conversation')
        response = authenticated_client.post(url, conversation_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        data = response.json()
        assert 'error' in data
        assert 'invalid' in data['error'].lower()
    
    def test_create_conversation_unauthenticated(self, api_client):
        """Test creating conversation without authentication."""
        conversation_data = {
            'type': 'DIRECT',
            'participant_ids': [str(UserFactory().id)]
        }
        
        url = reverse('create-conversation')
        response = api_client.post(url, conversation_data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

@pytest.mark.django_db
class TestConversationMessagesAPI:
    """Test conversation messages API endpoint."""
    
    def test_get_conversation_messages(self, authenticated_client, user):
        """Test getting messages for a conversation."""
        other_user = UserFactory()
        conversation = ConversationFactory()
        
        ConversationParticipantFactory(user=user, conversation=conversation)
        ConversationParticipantFactory(user=other_user, conversation=conversation)
        
        # Create messages
        messages = []
        for i in range(5):
            sender = user if i % 2 == 0 else other_user
            message = MessageFactory(
                conversation=conversation,
                sender=sender,
                content=f"Message {i}"
            )
            messages.append(message)
        
        url = reverse('conversation-messages', kwargs={'conversation_id': conversation.id})
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert 'results' in data
        assert len(data['results']) == 5
        
        # Check message structure
        message_data = data['results'][0]
        required_fields = [
            'id', 'conversation_id', 'sender', 'content',
            'message_type', 'created_at', 'updated_at'
        ]
        for field in required_fields:
            assert field in message_data
        
        # Check sender structure
        sender_data = message_data['sender']
        sender_fields = ['id', 'username', 'first_name', 'last_name', 'profile_picture']
        for field in sender_fields:
            assert field in sender_data
    
    def test_get_messages_not_participant(self, authenticated_client, user):
        """Test getting messages when user is not a participant."""
        other_user = UserFactory()
        conversation = ConversationFactory()
        
        # Only other_user is participant
        ConversationParticipantFactory(user=other_user, conversation=conversation)
        
        MessageFactory(conversation=conversation, sender=other_user)
        
        url = reverse('conversation-messages', kwargs={'conversation_id': conversation.id})
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        
        data = response.json()
        assert 'error' in data
        assert 'not a participant' in data['error'].lower()
    
    def test_get_messages_nonexistent_conversation(self, authenticated_client, user):
        """Test getting messages for nonexistent conversation."""
        import uuid
        
        url = reverse('conversation-messages', kwargs={'conversation_id': uuid.uuid4()})
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_get_messages_with_pagination(self, authenticated_client, user):
        """Test getting messages with pagination."""
        conversation = ConversationFactory()
        ConversationParticipantFactory(user=user, conversation=conversation)
        
        # Create many messages
        for i in range(60):
            MessageFactory(conversation=conversation, sender=user)
        
        url = reverse('conversation-messages', kwargs={'conversation_id': conversation.id})
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert 'results' in data
        assert 'count' in data
        assert 'next' in data
        assert 'previous' in data
        
        # Default page size should be 50
        assert len(data['results']) == 50
        assert data['count'] == 60
        assert data['next'] is not None
    
    def test_get_messages_unauthenticated(self, api_client):
        """Test getting messages without authentication."""
        conversation = ConversationFactory()

        url = reverse('conversation-messages', kwargs={'conversation_id': conversation.id})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

@pytest.mark.django_db
class TestSendMessageAPI:
    """Test send message API endpoint."""

    def test_send_message_success(self, authenticated_client, user):
        """Test sending a message successfully."""
        other_user = UserFactory()
        conversation = ConversationFactory()

        ConversationParticipantFactory(user=user, conversation=conversation)
        ConversationParticipantFactory(user=other_user, conversation=conversation)

        message_data = {
            'content': 'Hello, world!',
            'message_type': 'TEXT'
        }

        url = reverse('send-message', kwargs={'conversation_id': conversation.id})
        response = authenticated_client.post(url, message_data, format='json')

        assert response.status_code == status.HTTP_201_CREATED

        data = response.json()
        assert data['content'] == message_data['content']
        assert data['message_type'] == message_data['message_type']
        assert data['sender']['id'] == str(user.id)
        assert data['conversation_id'] == str(conversation.id)

        # Verify message was created in database
        message = Message.objects.get(id=data['id'])
        assert message.content == message_data['content']
        assert message.sender == user
        assert message.conversation == conversation

        # Verify conversation updated_at was updated
        conversation.refresh_from_db()
        assert conversation.updated_at is not None

    def test_send_message_not_participant(self, authenticated_client, user):
        """Test sending message when user is not a participant."""
        other_user = UserFactory()
        conversation = ConversationFactory()

        # Only other_user is participant
        ConversationParticipantFactory(user=other_user, conversation=conversation)

        message_data = {
            'content': 'Hello, world!',
            'message_type': 'TEXT'
        }

        url = reverse('send-message', kwargs={'conversation_id': conversation.id})
        response = authenticated_client.post(url, message_data, format='json')

        assert response.status_code == status.HTTP_403_FORBIDDEN

        data = response.json()
        assert 'error' in data
        assert 'not a participant' in data['error'].lower()

    def test_send_message_validation_errors(self, authenticated_client, user):
        """Test sending message with validation errors."""
        conversation = ConversationFactory()
        ConversationParticipantFactory(user=user, conversation=conversation)

        # Empty content
        message_data = {'content': ''}

        url = reverse('send-message', kwargs={'conversation_id': conversation.id})
        response = authenticated_client.post(url, message_data, format='json')

        assert response.status_code == status.HTTP_400_BAD_REQUEST

        # Content too long
        long_content = 'a' * 4001
        message_data = {'content': long_content}

        response = authenticated_client.post(url, message_data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        # Missing content
        response = authenticated_client.post(url, {}, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_send_message_different_types(self, authenticated_client, user):
        """Test sending different message types."""
        conversation = ConversationFactory()
        ConversationParticipantFactory(user=user, conversation=conversation)

        message_types = ['TEXT', 'IMAGE', 'FILE', 'SYSTEM']

        for msg_type in message_types:
            message_data = {
                'content': f'Test {msg_type} message',
                'message_type': msg_type
            }

            url = reverse('send-message', kwargs={'conversation_id': conversation.id})
            response = authenticated_client.post(url, message_data, format='json')

            assert response.status_code == status.HTTP_201_CREATED

            data = response.json()
            assert data['message_type'] == msg_type

    def test_send_message_nonexistent_conversation(self, authenticated_client, user):
        """Test sending message to nonexistent conversation."""
        import uuid

        message_data = {
            'content': 'Hello, world!',
            'message_type': 'TEXT'
        }

        url = reverse('send-message', kwargs={'conversation_id': uuid.uuid4()})
        response = authenticated_client.post(url, message_data, format='json')

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_send_message_unauthenticated(self, api_client):
        """Test sending message without authentication."""
        conversation = ConversationFactory()

        message_data = {
            'content': 'Hello, world!',
            'message_type': 'TEXT'
        }

        url = reverse('send-message', kwargs={'conversation_id': conversation.id})
        response = api_client.post(url, message_data, format='json')

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

@pytest.mark.django_db
class TestUserSearchAPI:
    """Test user search API endpoint."""

    def test_search_users_by_username(self, authenticated_client, user):
        """Test searching users by username."""
        # Create test users
        user1 = UserFactory(username='john_doe', first_name='John', last_name='Doe')
        user2 = UserFactory(username='jane_smith', first_name='Jane', last_name='Smith')
        user3 = UserFactory(username='bob_wilson', first_name='Bob', last_name='Wilson')

        url = reverse('search-users')
        response = authenticated_client.get(url, {'q': 'john'})

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert 'results' in data
        assert len(data['results']) == 1
        assert data['results'][0]['username'] == 'john_doe'

    def test_search_users_by_first_name(self, authenticated_client, user):
        """Test searching users by first name."""
        user1 = UserFactory(username='user1', first_name='Alice', last_name='Johnson')
        user2 = UserFactory(username='user2', first_name='Alice', last_name='Brown')
        user3 = UserFactory(username='user3', first_name='Bob', last_name='Smith')

        url = reverse('search-users')
        response = authenticated_client.get(url, {'q': 'Alice'})

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert len(data['results']) == 2

        usernames = [u['username'] for u in data['results']]
        assert 'user1' in usernames
        assert 'user2' in usernames

    def test_search_users_by_last_name(self, authenticated_client, user):
        """Test searching users by last name."""
        user1 = UserFactory(username='user1', first_name='John', last_name='Smith')
        user2 = UserFactory(username='user2', first_name='Jane', last_name='Johnson')

        url = reverse('search-users')
        response = authenticated_client.get(url, {'q': 'Smith'})

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert len(data['results']) == 1
        assert data['results'][0]['last_name'] == 'Smith'

    def test_search_users_case_insensitive(self, authenticated_client, user):
        """Test that user search is case insensitive."""
        user1 = UserFactory(username='TestUser', first_name='Test', last_name='User')

        url = reverse('search-users')

        # Test different cases
        for query in ['test', 'TEST', 'Test', 'tEsT']:
            response = authenticated_client.get(url, {'q': query})
            assert response.status_code == status.HTTP_200_OK

            data = response.json()
            assert len(data['results']) >= 1
            assert any(u['username'] == 'TestUser' for u in data['results'])

    def test_search_users_empty_query(self, authenticated_client, user):
        """Test searching with empty query."""
        url = reverse('search-users')
        response = authenticated_client.get(url, {'q': ''})

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert 'results' in data
        # Should return empty results for empty query
        assert len(data['results']) == 0

    def test_search_users_no_query_param(self, authenticated_client, user):
        """Test searching without query parameter."""
        url = reverse('search-users')
        response = authenticated_client.get(url)

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert len(data['results']) == 0

    def test_search_users_excludes_current_user(self, authenticated_client, user):
        """Test that search excludes the current user."""
        # Create other users
        UserFactory(username='other_user')

        url = reverse('search-users')
        response = authenticated_client.get(url, {'q': user.username})

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        # Should not include the current user in results
        user_ids = [u['id'] for u in data['results']]
        assert str(user.id) not in user_ids

    def test_search_users_response_structure(self, authenticated_client, user):
        """Test user search response structure."""
        test_user = UserFactory(
            username='testuser',
            first_name='Test',
            last_name='User',
            profile_picture='https://example.com/avatar.jpg'
        )

        url = reverse('search-users')
        response = authenticated_client.get(url, {'q': 'testuser'})

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert len(data['results']) == 1

        user_data = data['results'][0]
        required_fields = ['id', 'username', 'first_name', 'last_name', 'profile_picture']
        for field in required_fields:
            assert field in user_data

        assert user_data['username'] == 'testuser'
        assert user_data['first_name'] == 'Test'
        assert user_data['last_name'] == 'User'
        assert user_data['profile_picture'] == 'https://example.com/avatar.jpg'

    def test_search_users_unauthenticated(self, api_client):
        """Test searching users without authentication."""
        url = reverse('search-users')
        response = api_client.get(url, {'q': 'test'})

        assert response.status_code == status.HTTP_401_UNAUTHORIZED
