# backend/tests/test_schemas.py
import pytest
import uuid
from datetime import datetime
from pydantic import ValidationError
from messaging.schemas import (
    ConversationType, MessageType, ParticipantRole,
    UserBasic, MessageCreate, MessageResponse,
    ConversationCreate, ConversationResponse
)
from tests.factories import UserFactory, ConversationFactory, MessageFactory

@pytest.mark.unit
class TestEnumSchemas:
    """Test enum schemas."""
    
    def test_conversation_type_enum(self):
        """Test ConversationType enum values."""
        assert ConversationType.DIRECT == "DIRECT"
        assert ConversationType.GROUP == "GROUP"
        
        # Test valid values
        assert ConversationType("DIRECT") == ConversationType.DIRECT
        assert ConversationType("GROUP") == ConversationType.GROUP
        
        # Test invalid value
        with pytest.raises(ValueError):
            ConversationType("INVALID")
    
    def test_message_type_enum(self):
        """Test MessageType enum values."""
        assert MessageType.TEXT == "TEXT"
        assert MessageType.IMAGE == "IMAGE"
        assert MessageType.FILE == "FILE"
        assert MessageType.SYSTEM == "SYSTEM"
        
        # Test valid values
        for msg_type in ["TEXT", "IMAGE", "FILE", "SYSTEM"]:
            assert MessageType(msg_type) in MessageType
        
        # Test invalid value
        with pytest.raises(ValueError):
            MessageType("INVALID")
    
    def test_participant_role_enum(self):
        """Test ParticipantRole enum values."""
        assert ParticipantRole.ADMIN == "ADMIN"
        assert ParticipantRole.MEMBER == "MEMBER"
        
        # Test valid values
        assert ParticipantRole("ADMIN") == ParticipantRole.ADMIN
        assert ParticipantRole("MEMBER") == ParticipantRole.MEMBER
        
        # Test invalid value
        with pytest.raises(ValueError):
            ParticipantRole("INVALID")

@pytest.mark.unit
class TestUserBasicSchema:
    """Test UserBasic schema."""
    
    def test_valid_user_basic(self):
        """Test creating UserBasic with valid data."""
        user_data = {
            'id': uuid.uuid4(),
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'profile_picture': 'https://example.com/avatar.jpg'
        }
        
        user_basic = UserBasic(**user_data)
        
        assert user_basic.id == user_data['id']
        assert user_basic.username == user_data['username']
        assert user_basic.first_name == user_data['first_name']
        assert user_basic.last_name == user_data['last_name']
        assert user_basic.profile_picture == user_data['profile_picture']
    
    def test_user_basic_without_profile_picture(self):
        """Test UserBasic without profile picture."""
        user_data = {
            'id': uuid.uuid4(),
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        user_basic = UserBasic(**user_data)
        assert user_basic.profile_picture is None
    
    def test_user_basic_validation_errors(self):
        """Test UserBasic validation errors."""
        # Missing required fields
        with pytest.raises(ValidationError) as exc_info:
            UserBasic()
        
        errors = exc_info.value.errors()
        required_fields = {'id', 'username', 'first_name', 'last_name'}
        error_fields = {error['loc'][0] for error in errors}
        assert required_fields.issubset(error_fields)
        
        # Invalid UUID
        with pytest.raises(ValidationError) as exc_info:
            UserBasic(
                id='invalid-uuid',
                username='test',
                first_name='Test',
                last_name='User'
            )
        
        assert any(error['loc'][0] == 'id' for error in exc_info.value.errors())
    
    @pytest.mark.django_db
    def test_user_basic_from_django_model(self):
        """Test creating UserBasic from Django User model."""
        user = UserFactory()
        user_basic = UserBasic.model_validate(user)
        
        assert user_basic.id == user.id
        assert user_basic.username == user.username
        assert user_basic.first_name == user.first_name
        assert user_basic.last_name == user.last_name
        assert user_basic.profile_picture == user.profile_picture

@pytest.mark.unit
class TestMessageCreateSchema:
    """Test MessageCreate schema."""
    
    def test_valid_message_create(self):
        """Test creating MessageCreate with valid data."""
        message_data = {
            'content': 'Hello, world!',
            'message_type': MessageType.TEXT,
            'reply_to_id': uuid.uuid4()
        }
        
        message_create = MessageCreate(**message_data)
        
        assert message_create.content == message_data['content']
        assert message_create.message_type == message_data['message_type']
        assert message_create.reply_to_id == message_data['reply_to_id']
    
    def test_message_create_defaults(self):
        """Test MessageCreate with default values."""
        message_create = MessageCreate(content='Hello')
        
        assert message_create.content == 'Hello'
        assert message_create.message_type == MessageType.TEXT
        assert message_create.reply_to_id is None
    
    def test_message_create_validation_errors(self):
        """Test MessageCreate validation errors."""
        # Empty content
        with pytest.raises(ValidationError) as exc_info:
            MessageCreate(content='')
        
        assert any(
            error['loc'][0] == 'content' and 'at least 1 character' in error['msg']
            for error in exc_info.value.errors()
        )
        
        # Content too long
        long_content = 'a' * 4001
        with pytest.raises(ValidationError) as exc_info:
            MessageCreate(content=long_content)
        
        assert any(
            error['loc'][0] == 'content' and 'at most 4000 characters' in error['msg']
            for error in exc_info.value.errors()
        )
        
        # Missing content
        with pytest.raises(ValidationError) as exc_info:
            MessageCreate()
        
        assert any(error['loc'][0] == 'content' for error in exc_info.value.errors())
    
    def test_message_create_different_types(self):
        """Test MessageCreate with different message types."""
        for msg_type in MessageType:
            message_create = MessageCreate(
                content='Test content',
                message_type=msg_type
            )
            assert message_create.message_type == msg_type

@pytest.mark.unit
class TestMessageResponseSchema:
    """Test MessageResponse schema."""
    
    def test_valid_message_response(self):
        """Test creating MessageResponse with valid data."""
        user_data = {
            'id': uuid.uuid4(),
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        message_data = {
            'id': uuid.uuid4(),
            'conversation_id': uuid.uuid4(),
            'sender': UserBasic(**user_data),
            'content': 'Hello, world!',
            'message_type': MessageType.TEXT,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        message_response = MessageResponse(**message_data)
        
        assert message_response.id == message_data['id']
        assert message_response.conversation_id == message_data['conversation_id']
        assert message_response.sender == message_data['sender']
        assert message_response.content == message_data['content']
        assert message_response.message_type == message_data['message_type']
        assert message_response.created_at == message_data['created_at']
        assert message_response.updated_at == message_data['updated_at']
    
    @pytest.mark.django_db
    def test_message_response_from_django_model(self):
        """Test creating MessageResponse from Django Message model."""
        message = MessageFactory()
        
        # Create MessageResponse using the serialize_message function pattern
        message_response = MessageResponse(
            id=message.id,
            conversation_id=message.conversation.id,
            sender=UserBasic.model_validate(message.sender),
            content=message.content,
            message_type=message.message_type,
            created_at=message.created_at,
            updated_at=message.updated_at
        )
        
        assert message_response.id == message.id
        assert message_response.conversation_id == message.conversation.id
        assert message_response.sender.id == message.sender.id
        assert message_response.content == message.content
        assert message_response.message_type == message.message_type
    
    def test_message_response_validation_errors(self):
        """Test MessageResponse validation errors."""
        # Missing required fields
        with pytest.raises(ValidationError) as exc_info:
            MessageResponse()
        
        errors = exc_info.value.errors()
        required_fields = {
            'id', 'conversation_id', 'sender', 'content',
            'message_type', 'created_at', 'updated_at'
        }
        error_fields = {error['loc'][0] for error in errors}
        assert required_fields.issubset(error_fields)

@pytest.mark.unit
class TestConversationCreateSchema:
    """Test ConversationCreate schema."""
    
    def test_valid_conversation_create_direct(self):
        """Test creating ConversationCreate for direct conversation."""
        conversation_data = {
            'type': ConversationType.DIRECT,
            'participant_ids': [uuid.uuid4()]
        }
        
        conversation_create = ConversationCreate(**conversation_data)
        
        assert conversation_create.type == ConversationType.DIRECT
        assert conversation_create.name is None
        assert len(conversation_create.participant_ids) == 1
    
    def test_valid_conversation_create_group(self):
        """Test creating ConversationCreate for group conversation."""
        conversation_data = {
            'type': ConversationType.GROUP,
            'name': 'Test Group',
            'participant_ids': [uuid.uuid4(), uuid.uuid4()]
        }
        
        conversation_create = ConversationCreate(**conversation_data)
        
        assert conversation_create.type == ConversationType.GROUP
        assert conversation_create.name == 'Test Group'
        assert len(conversation_create.participant_ids) == 2
    
    def test_conversation_create_validation_errors(self):
        """Test ConversationCreate validation errors."""
        # Missing required fields
        with pytest.raises(ValidationError) as exc_info:
            ConversationCreate()
        
        errors = exc_info.value.errors()
        required_fields = {'type', 'participant_ids'}
        error_fields = {error['loc'][0] for error in errors}
        assert required_fields.issubset(error_fields)
        
        # Empty participant_ids
        with pytest.raises(ValidationError) as exc_info:
            ConversationCreate(
                type=ConversationType.DIRECT,
                participant_ids=[]
            )
        
        assert any(
            error['loc'][0] == 'participant_ids' and 'at least 1 item' in error['msg']
            for error in exc_info.value.errors()
        )
        
        # Group conversation without name
        with pytest.raises(ValidationError) as exc_info:
            ConversationCreate(
                type=ConversationType.GROUP,
                participant_ids=[uuid.uuid4()]
            )
        
        assert any(
            'Group conversations must have a name' in error['msg']
            for error in exc_info.value.errors()
        )
        
        # Name too long
        long_name = 'a' * 101
        with pytest.raises(ValidationError) as exc_info:
            ConversationCreate(
                type=ConversationType.GROUP,
                name=long_name,
                participant_ids=[uuid.uuid4()]
            )
        
        assert any(
            error['loc'][0] == 'name' and 'at most 100 characters' in error['msg']
            for error in exc_info.value.errors()
        )

@pytest.mark.unit
class TestConversationResponseSchema:
    """Test ConversationResponse schema."""

    def test_valid_conversation_response(self):
        """Test creating ConversationResponse with valid data."""
        user1_data = {
            'id': uuid.uuid4(),
            'username': 'user1',
            'first_name': 'User',
            'last_name': 'One'
        }

        user2_data = {
            'id': uuid.uuid4(),
            'username': 'user2',
            'first_name': 'User',
            'last_name': 'Two'
        }

        message_data = {
            'id': uuid.uuid4(),
            'conversation_id': uuid.uuid4(),
            'sender': UserBasic(**user1_data),
            'content': 'Last message',
            'message_type': MessageType.TEXT,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        conversation_data = {
            'id': uuid.uuid4(),
            'type': ConversationType.DIRECT,
            'name': None,
            'participants': [UserBasic(**user1_data), UserBasic(**user2_data)],
            'last_message': MessageResponse(**message_data),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        conversation_response = ConversationResponse(**conversation_data)

        assert conversation_response.id == conversation_data['id']
        assert conversation_response.type == ConversationType.DIRECT
        assert conversation_response.name is None
        assert len(conversation_response.participants) == 2
        assert conversation_response.last_message is not None
        assert conversation_response.created_at == conversation_data['created_at']
        assert conversation_response.updated_at == conversation_data['updated_at']

    def test_conversation_response_without_last_message(self):
        """Test ConversationResponse without last message."""
        user_data = {
            'id': uuid.uuid4(),
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User'
        }

        conversation_data = {
            'id': uuid.uuid4(),
            'type': ConversationType.DIRECT,
            'participants': [UserBasic(**user_data)],
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        conversation_response = ConversationResponse(**conversation_data)

        assert conversation_response.last_message is None
        assert conversation_response.name is None

    def test_group_conversation_response(self):
        """Test ConversationResponse for group conversation."""
        users_data = [
            {
                'id': uuid.uuid4(),
                'username': f'user{i}',
                'first_name': 'User',
                'last_name': str(i)
            } for i in range(3)
        ]

        conversation_data = {
            'id': uuid.uuid4(),
            'type': ConversationType.GROUP,
            'name': 'Test Group',
            'participants': [UserBasic(**user_data) for user_data in users_data],
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        conversation_response = ConversationResponse(**conversation_data)

        assert conversation_response.type == ConversationType.GROUP
        assert conversation_response.name == 'Test Group'
        assert len(conversation_response.participants) == 3

    @pytest.mark.django_db
    def test_conversation_response_from_django_model(self):
        """Test creating ConversationResponse from Django models."""
        conversation = ConversationFactory()
        user1 = UserFactory()
        user2 = UserFactory()

        # Add participants
        from messaging.models import ConversationParticipant
        ConversationParticipant.objects.create(
            conversation=conversation, user=user1
        )
        ConversationParticipant.objects.create(
            conversation=conversation, user=user2
        )

        # Add a message
        message = MessageFactory(conversation=conversation, sender=user1)

        # Create ConversationResponse using the serialize_conversation function pattern
        participants = [
            UserBasic.model_validate(p.user)
            for p in conversation.participants.all()
        ]

        last_message_data = MessageResponse(
            id=message.id,
            conversation_id=message.conversation.id,
            sender=UserBasic.model_validate(message.sender),
            content=message.content,
            message_type=message.message_type,
            created_at=message.created_at,
            updated_at=message.updated_at
        )

        conversation_response = ConversationResponse(
            id=conversation.id,
            type=conversation.type,
            name=conversation.name,
            participants=participants,
            last_message=last_message_data,
            created_at=conversation.created_at,
            updated_at=conversation.updated_at
        )

        assert conversation_response.id == conversation.id
        assert conversation_response.type == conversation.type
        assert len(conversation_response.participants) == 2
        assert conversation_response.last_message.id == message.id

    def test_conversation_response_validation_errors(self):
        """Test ConversationResponse validation errors."""
        # Missing required fields
        with pytest.raises(ValidationError) as exc_info:
            ConversationResponse()

        errors = exc_info.value.errors()
        required_fields = {
            'id', 'type', 'participants', 'created_at', 'updated_at'
        }
        error_fields = {error['loc'][0] for error in errors}
        assert required_fields.issubset(error_fields)

@pytest.mark.unit
class TestSchemaIntegration:
    """Test schema integration and complex scenarios."""

    def test_nested_schema_validation(self):
        """Test validation of nested schemas."""
        # Test MessageResponse with invalid UserBasic
        with pytest.raises(ValidationError) as exc_info:
            MessageResponse(
                id=uuid.uuid4(),
                conversation_id=uuid.uuid4(),
                sender={'invalid': 'data'},  # Invalid UserBasic
                content='Test',
                message_type=MessageType.TEXT,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

        # Should have validation errors for the nested sender field
        assert any(
            'sender' in str(error['loc']) for error in exc_info.value.errors()
        )

    def test_schema_serialization(self):
        """Test schema serialization to dict."""
        user_basic = UserBasic(
            id=uuid.uuid4(),
            username='testuser',
            first_name='Test',
            last_name='User'
        )

        user_dict = user_basic.model_dump(mode='json')

        assert 'id' in user_dict
        assert 'username' in user_dict
        assert 'first_name' in user_dict
        assert 'last_name' in user_dict
        assert 'profile_picture' in user_dict

        # Test that UUID is serialized properly
        assert isinstance(user_dict['id'], str)

    def test_schema_json_serialization(self):
        """Test schema JSON serialization."""
        message_create = MessageCreate(
            content='Test message',
            message_type=MessageType.TEXT
        )

        json_str = message_create.model_dump_json()
        assert 'content' in json_str
        assert 'message_type' in json_str
        assert 'TEXT' in json_str

    def test_schema_copy_and_update(self):
        """Test schema copy with updates."""
        original = MessageCreate(
            content='Original content',
            message_type=MessageType.TEXT
        )

        updated = original.model_copy(update={'content': 'Updated content'})

        assert original.content == 'Original content'
        assert updated.content == 'Updated content'
        assert updated.message_type == MessageType.TEXT
