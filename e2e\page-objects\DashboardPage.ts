import { Page, Locator, expect } from '@playwright/test';

export class DashboardPage {
  readonly page: Page;
  readonly header: Locator;
  readonly logoutButton: Locator;
  readonly connectionStatus: Locator;
  readonly userProfile: Locator;
  readonly conversationList: Locator;
  readonly chatArea: Locator;
  readonly messageInput: Locator;
  readonly sendButton: Locator;
  readonly newChatButton: Locator;
  readonly userSearchButton: Locator;
  readonly userSearchModal: Locator;
  readonly userSearchInput: Locator;
  readonly userSearchResults: Locator;
  readonly messageList: Locator;
  readonly typingIndicator: Locator;
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;
    this.header = page.locator('[data-testid="dashboard-header"], header');
    this.logoutButton = page.locator('[data-testid="logout-button"], button:has-text("Logout")');
    this.connectionStatus = page.locator('[data-testid="connection-status"], .connection-status');
    this.userProfile = page.locator('[data-testid="user-profile"], .user-profile');
    this.conversationList = page.locator('[data-testid="conversation-list"], .conversation-list');
    this.chatArea = page.locator('[data-testid="chat-area"], .chat-area');
    this.messageInput = page.locator('[data-testid="message-input"], textarea[placeholder*="message"]');
    this.sendButton = page.locator('[data-testid="send-button"], button[type="submit"]');
    this.newChatButton = page.locator('[data-testid="new-chat-button"], button:has-text("New Chat")');
    this.userSearchButton = page.locator('[data-testid="user-search-button"], button:has-text("Search")');
    this.userSearchModal = page.locator('[data-testid="user-search-modal"], .modal, .dialog');
    this.userSearchInput = page.locator('[data-testid="user-search-input"], input[placeholder*="search"]');
    this.userSearchResults = page.locator('[data-testid="user-search-results"], .search-results');
    this.messageList = page.locator('[data-testid="message-list"], .message-list');
    this.typingIndicator = page.locator('[data-testid="typing-indicator"], .typing-indicator');
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"], .loading, .spinner');
  }

  async goto() {
    await this.page.goto('/dashboard');
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    await expect(this.header).toBeVisible();
    await expect(this.conversationList).toBeVisible();
    await this.waitForConnectionEstablished();
  }

  async waitForConnectionEstablished() {
    // Wait for socket connection to be established
    await expect(this.connectionStatus).toContainText(/connected|online/i, { timeout: 10000 });
  }

  async logout() {
    await this.logoutButton.click();
    await this.page.waitForURL('**/login', { timeout: 10000 });
  }

  async isConnected(): Promise<boolean> {
    const statusText = await this.connectionStatus.textContent();
    return statusText?.toLowerCase().includes('connected') || statusText?.toLowerCase().includes('online') || false;
  }

  async waitForDisconnection() {
    await expect(this.connectionStatus).toContainText(/disconnected|offline|reconnecting/i, { timeout: 10000 });
  }

  // Conversation management
  async getConversationCount(): Promise<number> {
    const conversations = this.conversationList.locator('.conversation-item, [data-testid="conversation-item"]');
    return await conversations.count();
  }

  async selectConversation(index: number) {
    const conversations = this.conversationList.locator('.conversation-item, [data-testid="conversation-item"]');
    await conversations.nth(index).click();
    await this.waitForChatAreaLoad();
  }

  async selectConversationByName(name: string) {
    const conversation = this.conversationList.locator(`.conversation-item:has-text("${name}"), [data-testid="conversation-item"]:has-text("${name}")`);
    await conversation.click();
    await this.waitForChatAreaLoad();
  }

  async waitForChatAreaLoad() {
    await expect(this.chatArea).toBeVisible();
    await expect(this.messageInput).toBeVisible();
  }

  // User search and conversation creation
  async openUserSearch() {
    await this.newChatButton.click();
    await expect(this.userSearchModal).toBeVisible();
  }

  async searchUser(query: string) {
    await this.userSearchInput.fill(query);
    await this.page.waitForTimeout(500); // Wait for debounced search
  }

  async selectUserFromSearch(username: string) {
    const userResult = this.userSearchResults.locator(`[data-testid="user-result"]:has-text("${username}"), .user-result:has-text("${username}")`);
    const chatButton = userResult.locator('[data-testid="user-action-button"]');
    await expect(chatButton).toBeVisible();
    await chatButton.click();
  }

  async createDirectConversation(username: string) {
    await this.openUserSearch();
    await this.searchUser(username);
    await this.selectUserFromSearch(username);
    await this.waitForChatAreaLoad();
  }

  async closeUserSearch() {
    const closeButton = this.userSearchModal.locator('[data-testid="close-button"], button:has-text("Close"), .close');
    await closeButton.click();
    await expect(this.userSearchModal).toBeHidden();
  }

  // Message operations
  async sendMessage(message: string) {
    await this.messageInput.fill(message);
    await this.sendButton.click();
    await this.waitForMessageSent();
  }

  async sendMessageWithEnter(message: string) {
    await this.messageInput.fill(message);
    await this.messageInput.press('Enter');
    await this.waitForMessageSent();
  }

  async sendMessageWithShiftEnter(message: string) {
    await this.messageInput.fill(message);
    await this.messageInput.press('Shift+Enter');
    // Should not send message, just add new line
  }

  async waitForMessageSent() {
    // Wait for message to appear in the message list
    await this.page.waitForTimeout(1000);
  }

  async getMessageCount(): Promise<number> {
    const messages = this.messageList.locator('.message, [data-testid="message"]');
    return await messages.count();
  }

  async getLastMessage(): Promise<string> {
    const messages = this.messageList.locator('.message, [data-testid="message"]');
    const lastMessage = messages.last();
    return await lastMessage.textContent() || '';
  }

  async getMessageByIndex(index: number): Promise<string> {
    const messages = this.messageList.locator('.message, [data-testid="message"]');
    const message = messages.nth(index);
    return await message.textContent() || '';
  }

  async waitForNewMessage(timeout: number = 5000) {
    const initialCount = await this.getMessageCount();
    await expect(async () => {
      const currentCount = await this.getMessageCount();
      expect(currentCount).toBeGreaterThan(initialCount);
    }).toPass({ timeout });
  }

  async waitForTypingIndicator() {
    await expect(this.typingIndicator).toBeVisible({ timeout: 5000 });
  }

  async waitForTypingIndicatorToDisappear() {
    await expect(this.typingIndicator).toBeHidden({ timeout: 5000 });
  }

  async isTypingIndicatorVisible(): Promise<boolean> {
    return await this.typingIndicator.isVisible();
  }

  // Message input helpers
  async clearMessageInput() {
    await this.messageInput.clear();
  }

  async getMessageInputValue(): Promise<string> {
    return await this.messageInput.inputValue();
  }

  async isMessageInputDisabled(): Promise<boolean> {
    return await this.messageInput.isDisabled();
  }

  async isSendButtonDisabled(): Promise<boolean> {
    return await this.sendButton.isDisabled();
  }

  // Real-time features
  async simulateTyping(text: string) {
    await this.messageInput.focus();
    for (const char of text) {
      await this.messageInput.type(char);
      await this.page.waitForTimeout(100); // Simulate natural typing speed
    }
  }

  async stopTyping() {
    await this.messageInput.blur();
  }

  async getMessageCount(): Promise<number> {
    const messages = this.page.locator('[data-testid="message"]');
    return await messages.count();
  }

  async getLastMessage(): Promise<string> {
    const lastMessage = this.page.locator('[data-testid="message"]').last();
    return await lastMessage.textContent() || '';
  }

  async getAllMessages(): Promise<string[]> {
    const messages = this.page.locator('[data-testid="message"]');
    return await messages.allTextContents();
  }

  // Error handling
  async waitForErrorMessage() {
    const errorMessage = this.page.locator('[data-testid="error-message"], .error, .alert-error');
    await expect(errorMessage).toBeVisible({ timeout: 5000 });
  }

  async getErrorMessage(): Promise<string> {
    const errorMessage = this.page.locator('[data-testid="error-message"], .error, .alert-error');
    return await errorMessage.textContent() || '';
  }

  async dismissError() {
    const dismissButton = this.page.locator('[data-testid="dismiss-error"], .error .close, .alert-error .close');
    if (await dismissButton.isVisible()) {
      await dismissButton.click();
    }
  }

  // Loading states
  async isLoading(): Promise<boolean> {
    return await this.loadingSpinner.isVisible();
  }

  async waitForLoadingToFinish() {
    if (await this.isLoading()) {
      await expect(this.loadingSpinner).toBeHidden({ timeout: 10000 });
    }
  }

  // Accessibility helpers
  async checkAccessibility() {
    // Check for proper ARIA labels
    await expect(this.messageInput).toHaveAttribute('aria-label');
    await expect(this.sendButton).toHaveAttribute('aria-label');
    
    // Check for proper heading structure
    await expect(this.page.locator('h1, h2, h3')).toHaveCount({ min: 1 });
  }

  // Performance helpers
  async measureMessageSendTime(): Promise<number> {
    const startTime = Date.now();
    await this.sendMessage('Performance test message');
    const endTime = Date.now();
    return endTime - startTime;
  }

  async measurePageLoadTime(): Promise<number> {
    const startTime = Date.now();
    await this.goto();
    const endTime = Date.now();
    return endTime - startTime;
  }
}
