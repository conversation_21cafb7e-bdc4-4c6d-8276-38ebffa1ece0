#!/usr/bin/env python3
"""
Test script for Phase 4 Group Chat functionality
Tests the group management APIs and basic group operations
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatapp.settings')
django.setup()

from django.contrib.auth import get_user_model
from messaging.models import Conversation, ConversationParticipant, GroupEvent
from encryption.models import GroupSession, GroupMemberKey

User = get_user_model()

# API Configuration
BASE_URL = 'http://localhost:6000/api'
HEADERS = {'Content-Type': 'application/json'}

class GroupChatTester:
    def __init__(self):
        self.access_token = None
        self.test_users = []
        self.test_group = None
        
    def setup_test_users(self):
        """Create test users for group testing"""
        print("🔧 Setting up test users...")
        
        # Create test users
        test_user_data = [
            {'username': 'group_admin', 'email': '<EMAIL>', 'first_name': 'Admin', 'last_name': 'User'},
            {'username': 'group_member1', 'email': '<EMAIL>', 'first_name': 'Member', 'last_name': 'One'},
            {'username': 'group_member2', 'email': '<EMAIL>', 'first_name': 'Member', 'last_name': 'Two'},
        ]
        
        for user_data in test_user_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'password': 'testpass123'
                }
            )
            if created:
                user.set_password('testpass123')
                user.save()
            self.test_users.append(user)
            
        print(f"✅ Created/found {len(self.test_users)} test users")
        
    def authenticate(self):
        """Authenticate as the admin user"""
        print("🔐 Authenticating...")
        
        auth_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        try:
            response = requests.post(f'{BASE_URL}/auth/login/', json=auth_data, headers=HEADERS)
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get('access')
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def get_auth_headers(self):
        """Get headers with authentication token"""
        return {
            **HEADERS,
            'Authorization': f'Bearer {self.access_token}'
        }
    
    def test_create_group(self):
        """Test group creation"""
        print("📝 Testing group creation...")
        
        group_data = {
            'name': 'Test Group Chat',
            'description': 'A test group for Phase 4 functionality',
            'member_ids': [str(self.test_users[1].id), str(self.test_users[2].id)],
            'max_participants': 50,
            'is_public': False
        }
        
        try:
            response = requests.post(
                f'{BASE_URL}/messaging/groups/create/',
                json=group_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 201:
                self.test_group = response.json()
                print(f"✅ Group created successfully: {self.test_group.get('name')}")
                print(f"   Group ID: {self.test_group.get('id')}")
                print(f"   Participants: {len(self.test_group.get('participants', []))}")
                return True
            else:
                print(f"❌ Group creation failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Group creation error: {e}")
            return False
    
    def test_group_info_update(self):
        """Test updating group information"""
        if not self.test_group:
            print("❌ No test group available for update test")
            return False
            
        print("📝 Testing group info update...")
        
        update_data = {
            'name': 'Updated Test Group',
            'description': 'Updated description for testing'
        }
        
        try:
            response = requests.put(
                f'{BASE_URL}/messaging/groups/{self.test_group["id"]}/update/',
                json=update_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                updated_group = response.json()
                print(f"✅ Group updated successfully: {updated_group.get('name')}")
                return True
            else:
                print(f"❌ Group update failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Group update error: {e}")
            return False
    
    def test_database_models(self):
        """Test that database models are working correctly"""
        print("🗄️ Testing database models...")
        
        try:
            # Check if group was created in database
            group_count = Conversation.objects.filter(type='GROUP').count()
            print(f"✅ Found {group_count} group conversations in database")
            
            # Check participants
            if self.test_group:
                participants = ConversationParticipant.objects.filter(
                    conversation_id=self.test_group['id']
                ).count()
                print(f"✅ Found {participants} participants in test group")
                
                # Check group events
                events = GroupEvent.objects.filter(
                    conversation_id=self.test_group['id']
                ).count()
                print(f"✅ Found {events} group events")
                
                # Check group session (encryption)
                try:
                    group_session = GroupSession.objects.get(
                        conversation_id=self.test_group['id']
                    )
                    print(f"✅ Group encryption session created (epoch: {group_session.current_epoch})")
                    
                    # Check member keys
                    member_keys = GroupMemberKey.objects.filter(
                        group_session=group_session
                    ).count()
                    print(f"✅ Found {member_keys} encrypted group keys for members")
                    
                except GroupSession.DoesNotExist:
                    print("⚠️ Group encryption session not found (encryption may have failed)")
                
            return True
            
        except Exception as e:
            print(f"❌ Database model test error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all group functionality tests"""
        print("🚀 Starting Group Chat Functionality Tests")
        print("=" * 50)
        
        # Setup
        self.setup_test_users()
        
        if not self.authenticate():
            print("❌ Cannot proceed without authentication")
            return False
        
        # Run tests
        tests = [
            self.test_create_group,
            self.test_group_info_update,
            self.test_database_models,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                print("-" * 30)
            except Exception as e:
                print(f"❌ Test failed with exception: {e}")
                print("-" * 30)
        
        # Summary
        print("=" * 50)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Group chat functionality is working.")
        else:
            print("⚠️ Some tests failed. Check the output above for details.")
        
        return passed == total

if __name__ == '__main__':
    tester = GroupChatTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
