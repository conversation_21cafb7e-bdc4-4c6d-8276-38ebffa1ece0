# backend/tests/test_api_auth.py
import pytest
import json
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from tests.factories import UserFactory

User = get_user_model()

@pytest.mark.django_db
class TestAuthenticationAPI:
    """Test authentication API endpoints."""
    
    def test_register_success(self, api_client):
        """Test successful user registration."""
        user_data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'testpass123'
        }
        
        url = reverse('register')
        response = api_client.post(url, user_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        
        data = response.json()
        assert data['success'] is True
        assert 'data' in data
        assert 'user' in data['data']
        assert 'tokens' in data['data']
        
        # Check user data
        user_data_response = data['data']['user']
        assert user_data_response['email'] == user_data['email']
        assert user_data_response['username'] == user_data['username']
        assert user_data_response['first_name'] == user_data['first_name']
        assert user_data_response['last_name'] == user_data['last_name']
        
        # Check tokens
        tokens = data['data']['tokens']
        assert 'access' in tokens
        assert 'refresh' in tokens
        
        # Verify user was created in database
        user = User.objects.get(email=user_data['email'])
        assert user.username == user_data['username']
        assert user.check_password(user_data['password'])
    
    def test_register_validation_errors(self, api_client):
        """Test registration with validation errors."""
        # Missing required fields
        response = api_client.post(reverse('register'), {}, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        data = response.json()
        assert 'errors' in data
        
        # Invalid email
        invalid_data = {
            'email': 'invalid-email',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }
        
        response = api_client.post(reverse('register'), invalid_data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        # Short password
        short_password_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': '123'
        }
        
        response = api_client.post(reverse('register'), short_password_data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_register_duplicate_email(self, api_client):
        """Test registration with duplicate email."""
        existing_user = UserFactory(email='<EMAIL>')

        user_data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'testpass123'
        }

        response = api_client.post(reverse('register'), user_data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_register_duplicate_username(self, api_client):
        """Test registration with duplicate username."""
        existing_user = UserFactory(username='existinguser')

        user_data = {
            'email': '<EMAIL>',
            'username': 'existinguser',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'testpass123'
        }

        response = api_client.post(reverse('register'), user_data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_login_success(self, api_client):
        """Test successful login."""
        user = UserFactory(email='<EMAIL>')
        user.set_password('testpass123')
        user.save()
        
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = api_client.post(reverse('login'), login_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data['success'] is True
        assert 'data' in data
        assert 'user' in data['data']
        assert 'tokens' in data['data']
        
        # Check user data
        user_data = data['data']['user']
        assert user_data['email'] == user.email
        assert user_data['username'] == user.username
        
        # Check tokens
        tokens = data['data']['tokens']
        assert 'access' in tokens
        assert 'refresh' in tokens
    
    def test_login_invalid_credentials(self, api_client):
        """Test login with invalid credentials."""
        user = UserFactory(email='<EMAIL>')
        user.set_password('correctpass')
        user.save()
        
        # Wrong password
        login_data = {
            'email': '<EMAIL>',
            'password': 'wrongpass'
        }
        
        response = api_client.post(reverse('login'), login_data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        data = response.json()
        assert data['success'] is False
        assert 'error' in data
        assert data['error'] == 'Invalid credentials'
    
    def test_login_nonexistent_user(self, api_client):
        """Test login with nonexistent user."""
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = api_client.post(reverse('login'), login_data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        data = response.json()
        assert data['success'] is False
        assert data['error'] == 'Invalid credentials'
    
    def test_login_validation_errors(self, api_client):
        """Test login with validation errors."""
        # Missing fields
        response = api_client.post(reverse('login'), {}, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        # Invalid email format
        invalid_data = {
            'email': 'invalid-email',
            'password': 'testpass123'
        }
        
        response = api_client.post(reverse('login'), invalid_data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_profile_authenticated(self, authenticated_client, user):
        """Test getting profile with authentication."""
        response = authenticated_client.get(reverse('profile'))
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data['success'] is True
        assert 'data' in data
        
        user_data = data['data']
        assert user_data['email'] == user.email
        assert user_data['username'] == user.username
        assert user_data['first_name'] == user.first_name
        assert user_data['last_name'] == user.last_name
    
    def test_profile_unauthenticated(self, api_client):
        """Test getting profile without authentication."""
        response = api_client.get(reverse('profile'))
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_profile_invalid_token(self, api_client):
        """Test getting profile with invalid token."""
        api_client.credentials(HTTP_AUTHORIZATION='Bearer invalid-token')
        response = api_client.get(reverse('profile'))
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_jwt_token_functionality(self, user):
        """Test JWT token creation and validation."""
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        # Test that token works for authentication
        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        response = client.get(reverse('profile'))
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data['data']['id'] == str(user.id)

@pytest.mark.django_db
class TestAuthenticationEdgeCases:
    """Test authentication edge cases and security."""
    
    def test_case_insensitive_email_login(self, api_client):
        """Test that email login is case insensitive."""
        user = UserFactory(email='<EMAIL>')
        user.set_password('testpass123')
        user.save()
        
        # Login with lowercase email
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = api_client.post(reverse('login'), login_data, format='json')
        assert response.status_code == status.HTTP_200_OK
    
    def test_password_not_returned_in_responses(self, api_client):
        """Test that password is never returned in API responses."""
        user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }
        
        # Register
        response = api_client.post(reverse('register'), user_data, format='json')
        data = response.json()
        
        assert 'password' not in data['data']['user']
        
        # Login
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = api_client.post(reverse('login'), login_data, format='json')
        data = response.json()
        
        assert 'password' not in data['data']['user']
    
    def test_inactive_user_cannot_login(self, api_client):
        """Test that inactive users cannot login."""
        user = UserFactory(email='<EMAIL>', is_active=False)
        user.set_password('testpass123')
        user.save()
        
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = api_client.post(reverse('login'), login_data, format='json')
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
