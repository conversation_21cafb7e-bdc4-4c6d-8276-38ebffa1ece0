// frontend/src/services/api.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import type { ApiResponse, BaseQueryError } from '../types';

// Get base URL for API requests
const getBaseUrl = () => {
  // In test environment, use a full URL to avoid URL parsing issues
  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
    return 'http://localhost:3000/api';
  }
  // In browser environment, use relative URL
  return '/api';
};

// Custom base query with authentication and error handling
const baseQueryWithAuth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  // Get the URL being requested
  const url = typeof args === 'string' ? args : args.url;

  const baseQuery = fetchBaseQuery({
    baseUrl: getBaseUrl(),
    prepareHeaders: (headers, { endpoint }) => {
      const token = localStorage.getItem('token');

      // Debug logging to see what endpoint name and URL we're getting
      console.log('🔑 [AUTH] Preparing headers for endpoint:', endpoint, 'URL:', url, 'Token exists:', !!token);

      // Check if this is a public endpoint by URL or endpoint name
      const publicEndpoints = ['login', 'register'];
      const isPublicEndpoint = (endpoint && publicEndpoints.includes(endpoint)) ||
                              url.includes('/auth/login') ||
                              url.includes('/auth/register');

      // Always add token if it exists and it's not explicitly a public endpoint
      if (token && !isPublicEndpoint) {
        headers.set('authorization', `Bearer ${token}`);
        console.log('🔑 [AUTH] ✅ Added authorization header for:', endpoint || url);
      } else if (!token) {
        console.log('🔑 [AUTH] ❌ No token found in localStorage');
      } else {
        console.log('🔑 [AUTH] ⏭️ Skipping auth header for public endpoint:', endpoint || url);
      }

      // Only set content-type for JSON requests, let browser set it for FormData
      if (typeof args === 'object' && args.body && !(args.body instanceof FormData)) {
        headers.set('content-type', 'application/json');
      }

      return headers;
    },
  });

  let result = await baseQuery(args, api, extraOptions);

  // Handle 401 errors (token expired)
  if (result.error && result.error.status === 401) {
    // Clear tokens
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');

    // Only redirect if we're in a browser environment and not on auth pages
    if (typeof window !== 'undefined' &&
        window.location &&
        window.location.pathname !== '/login' &&
        window.location.pathname !== '/register') {
      window.location.href = '/login';
    }
  }

  return result;
};

// Main API slice
export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithAuth,
  tagTypes: [
    'User',
    'Conversation',
    'Message',
    'Auth',
    'Encryption'
  ],
  endpoints: (builder) => ({
    // Test endpoints for testing purposes
    test: builder.query<any, void>({
      query: () => '/test',
    }),
    testMutation: builder.mutation<any, any>({
      query: (data) => ({
        url: '/test',
        method: 'POST',
        body: data,
      }),
    }),
    upload: builder.mutation<any, FormData>({
      query: (data) => ({
        url: '/upload',
        method: 'POST',
        body: data,
      }),
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useTestQuery,
  useTestMutationMutation,
  useUploadMutation,
  // Base API hooks - these will be populated by injected endpoints
} = api;
