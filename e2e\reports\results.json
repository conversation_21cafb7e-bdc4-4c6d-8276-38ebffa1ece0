{"config": {"configFile": "D:\\AI PRojects\\ChatApplication\\playwright.config.ts", "rootDir": "D:/AI PRojects/ChatApplication/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "e2e/reports/html"}], ["json", {"outputFile": "e2e/reports/results.json"}], ["junit", {"outputFile": "e2e/reports/results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 6, "webServer": null}, "suites": [{"title": "comprehensive-priority-tests.spec.ts", "file": "comprehensive-priority-tests.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "🚀 Comprehensive Priority E2E Tests - Chat Application", "file": "comprehensive-priority-tests.spec.ts", "line": 108, "column": 6, "specs": [{"title": "🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 4904, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:14.970Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-chromium\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-3a9e9e079efaf969dfc4", "file": "comprehensive-priority-tests.spec.ts", "line": 136, "column": 7}, {"title": "🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 4829, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:14.889Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--chromium\\test-failed-2.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-b20511cc3414c0cf70f7", "file": "comprehensive-priority-tests.spec.ts", "line": 238, "column": 7}, {"title": "🎯 PRIORITY 3: Group Chat Creation and Notifications", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 5284, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 3: Group Chat Creation and Notifications\n"}, {"text": "\n📝 Step 1: Login all three users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:14.917Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-chromium\\test-failed-4.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-d7c48befec53bdb3cfd6", "file": "comprehensive-priority-tests.spec.ts", "line": 300, "column": 7}, {"title": "🎯 COMPREHENSIVE: All Features Integration Test", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 4710, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting COMPREHENSIVE: All Features Integration Test\n"}, {"text": "\n📝 Step 1: Complete authentication flow\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:14.928Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-chromium\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-a9b2145b29f1422f2757", "file": "comprehensive-priority-tests.spec.ts", "line": 405, "column": 7}, {"title": "🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "timedOut", "duration": 30061, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:110:8", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 8, "line": 110}, "snippet": "\u001b[0m \u001b[90m 108 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'🚀 Comprehensive Priority E2E Tests - Chat Application'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 109 |\u001b[39m   \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 110 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 111 |\u001b[39m     \u001b[90m// Set up network monitoring\u001b[39m\n \u001b[90m 112 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mroute(\u001b[32m'**/*'\u001b[39m\u001b[33m,\u001b[39m (route) \u001b[33m=>\u001b[39m {\n \u001b[90m 113 |\u001b[39m       \u001b[36mconst\u001b[39m url \u001b[33m=\u001b[39m route\u001b[33m.\u001b[39mrequest()\u001b[33m.\u001b[39murl()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 8, "line": 110}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 108 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'🚀 Comprehensive Priority E2E Tests - Chat Application'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 109 |\u001b[39m   \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 110 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 111 |\u001b[39m     \u001b[90m// Set up network monitoring\u001b[39m\n \u001b[90m 112 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mroute(\u001b[32m'**/*'\u001b[39m\u001b[33m,\u001b[39m (route) \u001b[33m=>\u001b[39m {\n \u001b[90m 113 |\u001b[39m       \u001b[36mconst\u001b[39m url \u001b[33m=\u001b[39m route\u001b[33m.\u001b[39mrequest()\u001b[33m.\u001b[39murl()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:110:8\u001b[22m"}, {"message": "Error: browserContext.newPage: Test timeout of 30000ms exceeded."}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:14.946Z", "annotations": [], "attachments": [], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 8, "line": 110}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-1cfff47d4c68b1500815", "file": "comprehensive-priority-tests.spec.ts", "line": 136, "column": 7}, {"title": "🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 28383, "error": {"message": "Error: page.goto: NS_ERROR_NET_EMPTY_RESPONSE\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: NS_ERROR_NET_EMPTY_RESPONSE\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: NS_ERROR_NET_EMPTY_RESPONSE\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:15.029Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--firefox\\test-failed-2.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-4022649cabb2ae535e23", "file": "comprehensive-priority-tests.spec.ts", "line": 238, "column": 7}, {"title": "🎯 PRIORITY 3: Group Chat Creation and Notifications", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 3, "status": "timedOut", "duration": 30060, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:110:8", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 8, "line": 110}, "snippet": "\u001b[0m \u001b[90m 108 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'🚀 Comprehensive Priority E2E Tests - Chat Application'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 109 |\u001b[39m   \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 110 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 111 |\u001b[39m     \u001b[90m// Set up network monitoring\u001b[39m\n \u001b[90m 112 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mroute(\u001b[32m'**/*'\u001b[39m\u001b[33m,\u001b[39m (route) \u001b[33m=>\u001b[39m {\n \u001b[90m 113 |\u001b[39m       \u001b[36mconst\u001b[39m url \u001b[33m=\u001b[39m route\u001b[33m.\u001b[39mrequest()\u001b[33m.\u001b[39murl()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 8, "line": 110}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 108 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'🚀 Comprehensive Priority E2E Tests - Chat Application'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 109 |\u001b[39m   \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 110 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 111 |\u001b[39m     \u001b[90m// Set up network monitoring\u001b[39m\n \u001b[90m 112 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mroute(\u001b[32m'**/*'\u001b[39m\u001b[33m,\u001b[39m (route) \u001b[33m=>\u001b[39m {\n \u001b[90m 113 |\u001b[39m       \u001b[36mconst\u001b[39m url \u001b[33m=\u001b[39m route\u001b[33m.\u001b[39mrequest()\u001b[33m.\u001b[39murl()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:110:8\u001b[22m"}, {"message": "Error: browserContext.newPage: Test timeout of 30000ms exceeded."}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:24.535Z", "annotations": [], "attachments": [], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 8, "line": 110}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-5d4ad3849e358f8cc62f", "file": "comprehensive-priority-tests.spec.ts", "line": 300, "column": 7}, {"title": "🎯 COMPREHENSIVE: All Features Integration Test", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "timedOut", "duration": 30253, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:110:8", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 8, "line": 110}, "snippet": "\u001b[0m \u001b[90m 108 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'🚀 Comprehensive Priority E2E Tests - Chat Application'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 109 |\u001b[39m   \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 110 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 111 |\u001b[39m     \u001b[90m// Set up network monitoring\u001b[39m\n \u001b[90m 112 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mroute(\u001b[32m'**/*'\u001b[39m\u001b[33m,\u001b[39m (route) \u001b[33m=>\u001b[39m {\n \u001b[90m 113 |\u001b[39m       \u001b[36mconst\u001b[39m url \u001b[33m=\u001b[39m route\u001b[33m.\u001b[39mrequest()\u001b[33m.\u001b[39murl()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 8, "line": 110}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 108 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'🚀 Comprehensive Priority E2E Tests - Chat Application'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 109 |\u001b[39m   \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 110 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m     |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 111 |\u001b[39m     \u001b[90m// Set up network monitoring\u001b[39m\n \u001b[90m 112 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mroute(\u001b[32m'**/*'\u001b[39m\u001b[33m,\u001b[39m (route) \u001b[33m=>\u001b[39m {\n \u001b[90m 113 |\u001b[39m       \u001b[36mconst\u001b[39m url \u001b[33m=\u001b[39m route\u001b[33m.\u001b[39mrequest()\u001b[33m.\u001b[39murl()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:110:8\u001b[22m"}, {"message": "Error: browserContext.newPage: Test timeout of 30000ms exceeded."}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:24.446Z", "annotations": [], "attachments": [], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 8, "line": 110}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-bc82f900d47a9cd33190", "file": "comprehensive-priority-tests.spec.ts", "line": 405, "column": 7}, {"title": "🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "failed", "duration": 14318, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:7", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:7\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:24.696Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-webkit\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-7f06850c5e5d919204fa", "file": "comprehensive-priority-tests.spec.ts", "line": 136, "column": 7}, {"title": "🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "failed", "duration": 13919, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:7", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:7\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:26.156Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--webkit\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-a7cf0982b8acf9fbc2f4", "file": "comprehensive-priority-tests.spec.ts", "line": 238, "column": 7}, {"title": "🎯 PRIORITY 3: Group Chat Creation and Notifications", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 2, "status": "failed", "duration": 13554, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:7", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:7\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 3: Group Chat Creation and Notifications\n"}, {"text": "\n📝 Step 1: Login all three users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:41.233Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\\test-failed-4.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-webkit\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-059ca4dc8b4239e8cdcf", "file": "comprehensive-priority-tests.spec.ts", "line": 300, "column": 7}, {"title": "🎯 COMPREHENSIVE: All Features Integration Test", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 11, "parallelIndex": 1, "status": "failed", "duration": 12821, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:7", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:7\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting COMPREHENSIVE: All Features Integration Test\n"}, {"text": "\n📝 Step 1: Complete authentication flow\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:41.374Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-webkit\\test-failed-2.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-d473eb7fbad183170e3a", "file": "comprehensive-priority-tests.spec.ts", "line": 405, "column": 7}, {"title": "🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 4, "status": "failed", "duration": 2653, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:52.503Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Chrome\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-43e742d3e20e13a9b45e", "file": "comprehensive-priority-tests.spec.ts", "line": 136, "column": 7}, {"title": "🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 13, "parallelIndex": 5, "status": "failed", "duration": 2269, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:52.892Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Chrome\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-3d0a3ebe58fccd758d35", "file": "comprehensive-priority-tests.spec.ts", "line": 238, "column": 7}, {"title": "🎯 PRIORITY 3: Group Chat Creation and Notifications", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 1, "status": "failed", "duration": 3090, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 3: Group Chat Creation and Notifications\n"}, {"text": "\n📝 Step 1: Login all three users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:56.543Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Chrome\\test-failed-4.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-710d41d340fcfaf86719", "file": "comprehensive-priority-tests.spec.ts", "line": 300, "column": 7}, {"title": "🎯 COMPREHENSIVE: All Features Integration Test", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 15, "parallelIndex": 2, "status": "failed", "duration": 2217, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting COMPREHENSIVE: All Features Integration Test\n"}, {"text": "\n📝 Step 1: Complete authentication flow\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:56.764Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Chrome\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-089255f3fb5d73e1ebdf", "file": "comprehensive-priority-tests.spec.ts", "line": 405, "column": 7}, {"title": "🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 5, "status": "failed", "duration": 13063, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:7", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:7\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:58.434Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Mobile-Safari\\test-failed-2.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-46b41a828b15a2583830", "file": "comprehensive-priority-tests.spec.ts", "line": 136, "column": 7}, {"title": "🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 17, "parallelIndex": 4, "status": "failed", "duration": 13313, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:7", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:7\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:58.495Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Mobile-Safari\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-10fd3ce0a82046dce529", "file": "comprehensive-priority-tests.spec.ts", "line": 238, "column": 7}, {"title": "🎯 PRIORITY 3: Group Chat Creation and Notifications", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 18, "parallelIndex": 3, "status": "failed", "duration": 12815, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:7", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:7\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 3: Group Chat Creation and Notifications\n"}, {"text": "\n📝 Step 1: Login all three users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:56:59.687Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Mobile-Safari\\test-failed-4.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-b7353f2b0809d48c3868", "file": "comprehensive-priority-tests.spec.ts", "line": 300, "column": 7}, {"title": "🎯 COMPREHENSIVE: All Features Integration Test", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 19, "parallelIndex": 0, "status": "failed", "duration": 13107, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:7", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('form') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m   \n \u001b[90m 36 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 37 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"email-input\"]'\u001b[39m\u001b[33m,\u001b[39m email)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:34:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:7\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting COMPREHENSIVE: All Features Integration Test\n"}, {"text": "\n📝 Step 1: Complete authentication flow\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:57:02.325Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Mobile-Safari\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 34}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-5e07be0c2cef3b84b768", "file": "comprehensive-priority-tests.spec.ts", "line": 405, "column": 7}, {"title": "🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 20, "parallelIndex": 2, "status": "failed", "duration": 2956, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:57:02.785Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Microsoft-Edge\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-a5103f139a576d4db6f6", "file": "comprehensive-priority-tests.spec.ts", "line": 136, "column": 7}, {"title": "🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 21, "parallelIndex": 1, "status": "failed", "duration": 2892, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:57:02.929Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Microsoft-Edge\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-7a4726a5d01a4c1261a4", "file": "comprehensive-priority-tests.spec.ts", "line": 238, "column": 7}, {"title": "🎯 PRIORITY 3: Group Chat Creation and Notifications", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "failed", "duration": 3377, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 3: Group Chat Creation and Notifications\n"}, {"text": "\n📝 Step 1: Login all three users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:57:12.821Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Microsoft-Edge\\test-failed-4.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-02664d6baf1baa0780eb", "file": "comprehensive-priority-tests.spec.ts", "line": 300, "column": 7}, {"title": "🎯 COMPREHENSIVE: All Features Integration Test", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 23, "parallelIndex": 4, "status": "failed", "duration": 2744, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting COMPREHENSIVE: All Features Integration Test\n"}, {"text": "\n📝 Step 1: Complete authentication flow\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:57:13.051Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Microsoft-Edge\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-0c8f954d5619a5a08c5a", "file": "comprehensive-priority-tests.spec.ts", "line": 405, "column": 7}, {"title": "🎯 PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "failed", "duration": 1610, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:163:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:57:15.294Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-ddee4-otifications-and-Encryption-Google-Chrome\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-da71cdc37172c30a95a4", "file": "comprehensive-priority-tests.spec.ts", "line": 136, "column": 7}, {"title": "🎯 PRIORITY 2: Media Sharing in One-to-One Chat (Text-based Simulation)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 25, "parallelIndex": 1, "status": "failed", "duration": 2275, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:251:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 2: Media Sharing in One-to-One Chat\n"}, {"text": "\n📝 Step 1: Login both users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:57:17.340Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-4b78e-Chat-Text-based-Simulation--Google-Chrome\\test-failed-3.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-0a353ca9244600f585d1", "file": "comprehensive-priority-tests.spec.ts", "line": 238, "column": 7}, {"title": "🎯 PRIORITY 3: Group Chat Creation and Notifications", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 26, "parallelIndex": 2, "status": "failed", "duration": 3057, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:315:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting PRIORITY 3: Group Chat Creation and Notifications\n"}, {"text": "\n📝 Step 1: Login all three users\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:57:17.411Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\\test-failed-2.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-38698--Creation-and-Notifications-Google-Chrome\\test-failed-4.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-733b75c3b787579b906b", "file": "comprehensive-priority-tests.spec.ts", "line": 300, "column": 7}, {"title": "🎯 COMPREHENSIVE: All Features Integration Test", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 27, "parallelIndex": 0, "status": "failed", "duration": 2178, "error": {"message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:13", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}, "message": "Error: page.goto: net::ERR_HTTP_RESPONSE_CODE_FAILURE at http://localhost:5173/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m loginUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m password\u001b[33m:\u001b[39m string) {\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m`🔐 Logging in user: ${email}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[33mFRONTEND_URL\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m   \n \u001b[90m 33 |\u001b[39m   \u001b[90m// Wait for login form to be visible\u001b[39m\n \u001b[90m 34 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'form'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:31:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts:417:13\u001b[22m"}], "stdout": [{"text": "\n🚀 Starting COMPREHENSIVE: All Features Integration Test\n"}, {"text": "\n📝 Step 1: Complete authentication flow\n"}, {"text": "🔐 Logging in user: <EMAIL>\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-06T04:57:17.567Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\\video.webm"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\\test-failed-3.png"}, {"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\comprehensive-priority-tes-51baf-l-Features-Integration-Test-Google-Chrome\\test-failed-2.png"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\comprehensive-priority-tests.spec.ts", "column": 14, "line": 31}}], "status": "unexpected"}], "id": "46b01015ce7349a8ee20-85b3391d8d74b0404751", "file": "comprehensive-priority-tests.spec.ts", "line": 405, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-09-06T04:56:13.530Z", "duration": 71629.264, "expected": 0, "skipped": 0, "unexpected": 28, "flaky": 0}}