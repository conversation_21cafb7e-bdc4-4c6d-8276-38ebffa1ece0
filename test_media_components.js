#!/usr/bin/env node

/**
 * Quick test script to verify Phase 5 Media Sharing components are properly integrated
 * This script checks that all the necessary files exist and have the expected exports
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Phase 5 Media Sharing Implementation');
console.log('=' .repeat(60));

const tests = [];
let passed = 0;
let failed = 0;

function test(name, testFn) {
  tests.push({ name, testFn });
}

function runTests() {
  tests.forEach(({ name, testFn }) => {
    try {
      testFn();
      console.log(`✅ ${name}`);
      passed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      failed++;
    }
  });
  
  console.log('\n' + '='.repeat(60));
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All Phase 5 components are properly integrated!');
  } else {
    console.log('⚠️  Some components need attention.');
  }
}

// Test backend components
test('Backend media models exist', () => {
  const modelsPath = path.join(__dirname, 'backend', 'media', 'models.py');
  if (!fs.existsSync(modelsPath)) {
    throw new Error('Media models file not found');
  }
  
  const content = fs.readFileSync(modelsPath, 'utf8');
  if (!content.includes('class MediaFile')) {
    throw new Error('MediaFile model not found');
  }
  if (!content.includes('class MediaDownload')) {
    throw new Error('MediaDownload model not found');
  }
  if (!content.includes('class MediaChunk')) {
    throw new Error('MediaChunk model not found');
  }
});

test('Backend media views exist', () => {
  const viewsPath = path.join(__dirname, 'backend', 'media', 'views.py');
  if (!fs.existsSync(viewsPath)) {
    throw new Error('Media views file not found');
  }
  
  const content = fs.readFileSync(viewsPath, 'utf8');
  if (!content.includes('upload_media_simple')) {
    throw new Error('Simple upload view not found');
  }
  if (!content.includes('upload_media_chunked')) {
    throw new Error('Chunked upload view not found');
  }
  if (!content.includes('download_media')) {
    throw new Error('Download view not found');
  }
});

test('Backend media API service exists', () => {
  const apiPath = path.join(__dirname, 'backend', 'media', 'urls.py');
  if (!fs.existsSync(apiPath)) {
    throw new Error('Media URLs file not found');
  }
  
  const content = fs.readFileSync(apiPath, 'utf8');
  if (!content.includes('upload/simple/')) {
    throw new Error('Simple upload URL not found');
  }
  if (!content.includes('upload/chunked/')) {
    throw new Error('Chunked upload URL not found');
  }
});

test('Socket server media schemas exist', () => {
  const schemasPath = path.join(__dirname, 'socket-server', 'src', 'schemas', 'mediaSchemas.ts');
  if (!fs.existsSync(schemasPath)) {
    throw new Error('Media schemas file not found');
  }
  
  const content = fs.readFileSync(schemasPath, 'utf8');
  if (!content.includes('MediaUploadStartedSchema')) {
    throw new Error('MediaUploadStartedSchema not found');
  }
  if (!content.includes('MediaUploadCompletedSchema')) {
    throw new Error('MediaUploadCompletedSchema not found');
  }
});

test('Socket server media events integrated', () => {
  const eventsPath = path.join(__dirname, 'socket-server', 'src', 'events', 'socketEvents.ts');
  if (!fs.existsSync(eventsPath)) {
    throw new Error('Socket events file not found');
  }
  
  const content = fs.readFileSync(eventsPath, 'utf8');
  if (!content.includes('media_upload_started')) {
    throw new Error('Media upload started event not found');
  }
  if (!content.includes('handleMediaUploadCompleted')) {
    throw new Error('Media upload completed handler not found');
  }
});

test('Frontend media API service exists', () => {
  const apiPath = path.join(__dirname, 'frontend', 'src', 'services', 'mediaApi.ts');
  if (!fs.existsSync(apiPath)) {
    throw new Error('Frontend media API service not found');
  }
  
  const content = fs.readFileSync(apiPath, 'utf8');
  if (!content.includes('class MediaApiService')) {
    throw new Error('MediaApiService class not found');
  }
  if (!content.includes('uploadSimple')) {
    throw new Error('uploadSimple method not found');
  }
  if (!content.includes('uploadChunked')) {
    throw new Error('uploadChunked method not found');
  }
});

test('Frontend MediaUpload component exists', () => {
  const componentPath = path.join(__dirname, 'frontend', 'src', 'components', 'Chat', 'MediaUpload.tsx');
  if (!fs.existsSync(componentPath)) {
    throw new Error('MediaUpload component not found');
  }
  
  const content = fs.readFileSync(componentPath, 'utf8');
  if (!content.includes('export const MediaUpload')) {
    throw new Error('MediaUpload export not found');
  }
  if (!content.includes('Drag') && !content.includes('drag')) {
    throw new Error('Drag and drop functionality not found');
  }
});

test('Frontend MediaMessage component exists', () => {
  const componentPath = path.join(__dirname, 'frontend', 'src', 'components', 'Chat', 'MediaMessage.tsx');
  if (!fs.existsSync(componentPath)) {
    throw new Error('MediaMessage component not found');
  }
  
  const content = fs.readFileSync(componentPath, 'utf8');
  if (!content.includes('export const MediaMessage')) {
    throw new Error('MediaMessage export not found');
  }
  if (!content.includes('downloadFile')) {
    throw new Error('Download functionality not found');
  }
});

test('Frontend MessageInput integration', () => {
  const inputPath = path.join(__dirname, 'frontend', 'src', 'components', 'Chat', 'MessageInput.tsx');
  if (!fs.existsSync(inputPath)) {
    throw new Error('MessageInput component not found');
  }
  
  const content = fs.readFileSync(inputPath, 'utf8');
  if (!content.includes('MediaUpload')) {
    throw new Error('MediaUpload not integrated in MessageInput');
  }
  if (!content.includes('handleUploadStart')) {
    throw new Error('Upload handlers not found in MessageInput');
  }
});

test('Frontend MessageItem integration', () => {
  const itemPath = path.join(__dirname, 'frontend', 'src', 'components', 'Chat', 'MessageItem.tsx');
  if (!fs.existsSync(itemPath)) {
    throw new Error('MessageItem component not found');
  }
  
  const content = fs.readFileSync(itemPath, 'utf8');
  if (!content.includes('MediaMessage')) {
    throw new Error('MediaMessage not integrated in MessageItem');
  }
  if (!content.includes('mediaFiles')) {
    throw new Error('mediaFiles prop not found in MessageItem');
  }
});

test('Frontend SocketContext media events', () => {
  const contextPath = path.join(__dirname, 'frontend', 'src', 'contexts', 'SocketContext.tsx');
  if (!fs.existsSync(contextPath)) {
    throw new Error('SocketContext not found');
  }
  
  const content = fs.readFileSync(contextPath, 'utf8');
  if (!content.includes('media_upload_started')) {
    throw new Error('Media upload events not found in SocketContext');
  }
  if (!content.includes('handleMediaMessageReceived')) {
    throw new Error('Media message handler not found in SocketContext');
  }
});

test('Backend media app in settings', () => {
  const settingsPath = path.join(__dirname, 'backend', 'chatapp', 'settings.py');
  if (!fs.existsSync(settingsPath)) {
    throw new Error('Django settings file not found');
  }
  
  const content = fs.readFileSync(settingsPath, 'utf8');
  if (!content.includes("'media'")) {
    throw new Error('Media app not added to INSTALLED_APPS');
  }
});

test('Backend media URLs in main config', () => {
  const urlsPath = path.join(__dirname, 'backend', 'chatapp', 'urls.py');
  if (!fs.existsSync(urlsPath)) {
    throw new Error('Django URLs file not found');
  }
  
  const content = fs.readFileSync(urlsPath, 'utf8');
  if (!content.includes('api/media/')) {
    throw new Error('Media URLs not included in main URL config');
  }
});

test('Database migrations exist', () => {
  const migrationsDir = path.join(__dirname, 'backend', 'media', 'migrations');
  if (!fs.existsSync(migrationsDir)) {
    throw new Error('Media migrations directory not found');
  }
  
  const files = fs.readdirSync(migrationsDir);
  const migrationFiles = files.filter(f => f.startsWith('0001_') && f.endsWith('.py'));
  if (migrationFiles.length === 0) {
    throw new Error('Initial media migration not found');
  }
});

// Run all tests
runTests();

console.log('\n📋 Next Steps:');
console.log('1. Start all servers (Django, Socket, Frontend)');
console.log('2. Run the integration test: python backend/test_media_integration.py');
console.log('3. Follow the manual testing guide in test_complete_media_workflow.md');
console.log('4. Test the complete workflow in the browser');

if (failed === 0) {
  process.exit(0);
} else {
  process.exit(1);
}
