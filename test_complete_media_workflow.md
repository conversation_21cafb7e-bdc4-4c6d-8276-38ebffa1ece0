# Complete Media Sharing Workflow Test

This document outlines the complete end-to-end testing procedure for Phase 5 Media Sharing functionality.

## Prerequisites

1. **Backend Server Running**: Django server on port 6000
2. **Socket Server Running**: Node.js socket server on port 7000  
3. **Frontend Running**: React frontend on port 5000
4. **Database**: All Phase 5 migrations applied

## Test Scenarios

### 1. Basic Media Upload Test

**Objective**: Test simple file upload through the chat interface

**Steps**:
1. Open the chat application in browser
2. Navigate to any conversation
3. Click the upload button (📎) in the message input area
4. Select a small image file (< 10MB)
5. Verify file preview appears in upload area
6. Click "Upload" button
7. Verify upload progress indicator
8. Verify media message appears in chat
9. Verify other participants receive the media message via WebSocket

**Expected Results**:
- File uploads successfully
- Progress indicator shows upload progress
- Media message displays with thumbnail (for images)
- Download button is available
- Socket events are fired correctly

### 2. Large File Chunked Upload Test

**Objective**: Test chunked upload for large files

**Steps**:
1. Select a large file (> 10MB, < 100MB)
2. Initiate upload
3. Monitor chunked upload progress
4. Verify successful completion

**Expected Results**:
- File is uploaded in chunks
- Progress updates correctly
- Final assembly succeeds
- Media message appears correctly

### 3. Media Download Test

**Objective**: Test secure media download functionality

**Steps**:
1. Click download button on a media message
2. Verify download token generation
3. Verify file download starts
4. Check downloaded file integrity

**Expected Results**:
- Download token generated successfully
- File downloads correctly
- Downloaded file matches original

### 4. Real-time Media Notifications Test

**Objective**: Test WebSocket notifications for media operations

**Steps**:
1. Open chat in two browser windows/tabs
2. Upload media in one window
3. Verify real-time notifications in other window

**Expected Results**:
- Upload start notification received
- Progress updates received
- Completion notification received
- Media message appears in real-time

### 5. Media Message Display Test

**Objective**: Test media message rendering and interaction

**Steps**:
1. Upload different file types (image, document, video, audio)
2. Verify correct icons and previews
3. Test thumbnail display for images
4. Test file info display (name, size, type)

**Expected Results**:
- Correct file type icons displayed
- Image thumbnails generated and displayed
- File metadata shown correctly
- Media messages styled appropriately

### 6. Error Handling Test

**Objective**: Test error scenarios and recovery

**Steps**:
1. Try uploading oversized file (> 100MB)
2. Try uploading dangerous file type (.exe)
3. Simulate network interruption during upload
4. Test virus scanning rejection (if configured)

**Expected Results**:
- Appropriate error messages displayed
- Upload gracefully fails with cleanup
- User can retry failed uploads
- Error notifications sent via WebSocket

## Manual Testing Checklist

### Frontend Components
- [ ] MediaUpload component renders correctly
- [ ] Drag and drop functionality works
- [ ] File picker opens and accepts files
- [ ] Upload progress indicators work
- [ ] MediaMessage component displays correctly
- [ ] Download functionality works
- [ ] Thumbnail preview works for images
- [ ] File type icons display correctly

### Backend API
- [ ] `/api/media/upload/simple/` endpoint works
- [ ] `/api/media/upload/chunked/` endpoint works
- [ ] `/api/media/download/{media_id}/` endpoint works
- [ ] `/api/media/download/{token}/` endpoint works
- [ ] `/api/media/thumbnail/{media_id}/` endpoint works
- [ ] File validation works correctly
- [ ] Database records created properly

### Socket Server
- [ ] `media_upload_started` event handled
- [ ] `media_upload_progress` event handled
- [ ] `media_upload_completed` event handled
- [ ] `media_upload_failed` event handled
- [ ] `media_message_received` event handled
- [ ] `media_download_started` event handled
- [ ] Events broadcast to conversation participants

### Integration
- [ ] Media messages integrate with existing chat
- [ ] Message list displays media messages correctly
- [ ] Real-time updates work across clients
- [ ] Media files associate with messages properly
- [ ] Conversation last message updates for media

## Performance Testing

### Upload Performance
- Test upload speed for various file sizes
- Monitor memory usage during chunked uploads
- Verify cleanup of temporary chunks

### Download Performance  
- Test download speed and streaming
- Verify token expiration handling
- Monitor concurrent download limits

### UI Responsiveness
- Verify UI remains responsive during uploads
- Test multiple simultaneous uploads
- Check for memory leaks in file previews

## Security Testing

### File Validation
- Test file size limits by type
- Verify dangerous extension blocking
- Test MIME type validation

### Access Control
- Verify conversation participant access only
- Test download token security
- Verify file encryption (when implemented)

### Input Sanitization
- Test filename sanitization
- Verify metadata validation
- Test malformed upload requests

## Browser Compatibility

Test in multiple browsers:
- [ ] Chrome
- [ ] Firefox  
- [ ] Safari
- [ ] Edge

Test responsive design:
- [ ] Desktop
- [ ] Tablet
- [ ] Mobile

## Automated Testing

Run the existing test suite:
```bash
# Backend tests
cd backend
python manage.py test

# Frontend tests  
cd frontend
npm test

# Socket server tests
cd socket-server
npm test

# Integration test
cd backend
python test_media_integration.py
```

## Success Criteria

The Phase 5 Media Sharing implementation is considered successful when:

1. ✅ All manual test scenarios pass
2. ✅ All automated tests pass
3. ✅ No critical security vulnerabilities
4. ✅ Performance meets requirements
5. ✅ Cross-browser compatibility confirmed
6. ✅ Real-time functionality works correctly
7. ✅ Error handling is robust
8. ✅ User experience is intuitive

## Known Limitations

Current implementation limitations to be addressed in future phases:

1. **Encryption**: Media files are not yet fully encrypted (placeholder implementation)
2. **Virus Scanning**: Basic placeholder - needs ClamAV integration
3. **Thumbnail Generation**: Placeholder implementation for encrypted thumbnails
4. **Mobile Optimization**: May need additional mobile-specific optimizations
5. **Offline Support**: No offline upload queue yet

## Next Steps

After successful testing:

1. Address any identified issues
2. Optimize performance bottlenecks
3. Enhance error messaging
4. Add additional file type support
5. Implement full E2EE for media files
6. Add virus scanning integration
7. Optimize mobile experience
